<!-- 添加设备页面 - 按原始设计结构重构 -->
<wxs src="../../../wxs/tool.wxs" module="tool"></wxs>
<view class="page page-bg">
  <!-- 自定义导航栏 -->
  <van-nav-bar title="添加设备" left-arrow="{{true}}" arrow-direction="left" bind:click-left="goBack" background="transparent" border="{{false}}" />
  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 设备信息卡片 - 在上方 -->
    <view class="form-card device-info-card">
      <view class="form-content">
        <view class="form-title">设备信息</view>
        <!-- 设备名称 -->
        <view class="form-item-row">
          <view class="cell-title">设备名称</view>
          <van-field value="{{formData.device_name}}" placeholder="请输入" data-field="device_name" bind:change="onFieldChange" border="{{false}}" custom-class="custom-field" />
        </view>
        <!-- 设备SN码 -->
        <view class="form-item-row">
          <view class="cell-title">设备SN码</view>
          <van-field value="{{formData.device_sn}}" placeholder="请输入" data-field="device_sn" bind:change="onFieldChange" border="{{false}}" custom-class="custom-field" />
        </view>
        <!-- 设备类型 -->
        <view class="form-item-row">
          <view class="cell-title">设备类型</view>
          <picker mode="selector" range="{{typeColumns}}" value="{{selectedTypeIndex}}" bind:change="onTypeChange" active-class="active-class">
            <view class="picker-display">
              {{formData.device_type || '请选择设备类型'}}
              <van-icon name="arrow-down" size="16px" />
            </view>
          </picker>
        </view>
        <!-- 保修年限 -->
        <view class="form-item-row">
          <view class="cell-title">保修年限</view>
          <picker mode="selector" range="{{warrantyColumns}}" value="{{selectedWarrantyIndex}}" bind:change="onWarrantyChange" active-class="active-class">
            <view class="picker-display">
              {{formData.repair_period ? formData.repair_period + '年' : '请选择保修年限'}}
              <van-icon name="arrow-down" size="16px" />
            </view>
          </picker>
        </view>
        <!-- 设备照片 -->
        <view class="form-item">
          <view class="form-label">设备照片</view>
          <view class="image-upload-container">
            <!-- 已选择的图片 -->
            <view class="selected-image" wx:if="{{selectedImage}}">
              <image src="{{tool.cdn(selectedImage)}}" mode="aspectFill" class="device-image" />
              <view class="image-delete" bindtap="deleteImage">
                <van-icon name="cross" size="16px" color="#fff" />
              </view>
            </view>
            <!-- 上传按钮 -->
            <view class="upload-area" wx:if="{{!selectedImage}}" bindtap="chooseImage">
              <van-icon name="photograph" class="upload-icon" />
              <text class="upload-text">添加图片</text>
            </view>
          </view>
        </view>
        <!-- 备注 -->
        <view class="form-item">
          <view class="form-label">备注</view>
          <view class="remark-area">
            <textarea class="remark-input" placeholder="请输入备注" value="{{formData.remarks}}" maxlength="500" data-field="remarks" bind:input="onFieldChange" />
            <view class="remark-counter">{{remarkLength}} / 500</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 用户信息卡片 - 在底部，使用固定定位 -->
  <view class="user-info-card">
    <view class="form-content">
      <view class="form-title">用户信息</view>
      <!-- 用户选择区域 -->
      <view class="user-select-area" bindtap="selectUser">
        <view class="user-avatar">
          <image wx:if="{{userAvatar}}" src="{{tool.cdn(userAvatar)}}" mode="aspectFill" />
        </view>
        <text class="user-name">{{userInfo || '请选择用户'}}</text>
        <van-icon name="arrow" class="select-arrow" />
      </view>
      <!-- 真实姓名 -->
      <view class="form-item-row">
        <view class="cell-title">真实姓名</view>
        <van-field value="{{formData.name}}" placeholder="请输入" data-field="name" bind:change="onFieldChange" border="{{false}}" custom-class="custom-field" />
      </view>
    </view>
  </view>
  <!-- 提交按钮 - 在user-info-card内部 -->
  <view class="submit-button-wrapper">
    <van-button type="primary" size="large" loading="{{submitting}}" disabled="{{submitting}}" bind:click="submitForm" custom-class="submit-button">
      {{submitting ? '提交中...' : '确定添加'}}
    </van-button>
  </view>
</view>