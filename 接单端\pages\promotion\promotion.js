// 分销推广页面
const app = getApp();
const http = require('../../utils/http');
const util = require('../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {
      name: '王思思',
      commission: '25'
    },
    withdrawableAmount: '23,600.00',
    totalCommission: '390,200',
    withdrawnAmount: '390,200',
    totalTransactionAmount: '390,200',
    invitationCount: '35'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('分销推广页面加载', options);
    this.loadPromotionData();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('分销推广页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('分销推广页面显示');
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('分销推广页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('分销推广页面卸载');
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadPromotionData().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '分销推广',
      path: '/pages/promotion/promotion'
    };
  },

  /**
   * 加载分销推广数据
   */
  loadPromotionData() {
    return new Promise((resolve, reject) => {
      // TODO: 调用分销推广相关接口
      // http.get('promotion/info').then(res => {
      //   this.setData({
      //     userInfo: res.data.userInfo,
      //     withdrawableAmount: res.data.withdrawableAmount,
      //     totalCommission: res.data.totalCommission,
      //     withdrawnAmount: res.data.withdrawnAmount,
      //     totalTransactionAmount: res.data.totalTransactionAmount,
      //     invitationCount: res.data.invitationCount
      //   });
      //   resolve();
      // }).catch(err => {
      //   console.error('加载分销推广数据失败:', err);
      //   util.toast('加载失败，请重试');
      //   reject(err);
      // });
      
      // 暂时使用模拟数据
      setTimeout(() => {
        resolve();
      }, 500);
    });
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  },

  /**
   * 推广码点击事件
   */
  onPromotionCodeTap() {
    console.log('点击推广码');
    // TODO: 显示推广码详情或复制推广码
    wx.showModal({
      title: '推广码',
      content: '功能开发中...',
      showCancel: false
    });
  },

  /**
   * 我要提现点击事件
   */
  onWithdrawTap() {
    console.log('点击我要提现');
    // TODO: 跳转到提现页面
    wx.showModal({
      title: '提现',
      content: '功能开发中...',
      showCancel: false
    });
  },

  /**
   * 佣金明细点击事件
   */
  onCommissionDetailTap() {
    console.log('点击佣金明细');
    // TODO: 跳转到佣金明细页面
    wx.showModal({
      title: '佣金明细',
      content: '功能开发中...',
      showCancel: false
    });
  },

  /**
   * 提现记录点击事件
   */
  onWithdrawRecordTap() {
    console.log('点击提现记录');
    // TODO: 跳转到提现记录页面
    wx.showModal({
      title: '提现记录',
      content: '功能开发中...',
      showCancel: false
    });
  },

  /**
   * 我的邀请点击事件
   */
  onMyInvitationTap() {
    console.log('点击我的邀请');
    // TODO: 跳转到我的邀请页面
    wx.showModal({
      title: '我的邀请',
      content: '功能开发中...',
      showCancel: false
    });
  }
});
