<!-- 设备详情页面 -->
<wxs src="../../../wxs/tool.wxs" module="tool"></wxs>
<view class="page-bg">
  <!-- 自定义导航栏 -->
  <van-nav-bar 
    title="设备详情" 
    left-arrow="{{true}}"
    arrow-direction="left"
    bind:click-left="onClickLeft"
    custom-style="background: transparent; border-bottom: none;"
    title-style="color: #333333; font-weight: 500;"
    left-arrow-style="font-size: 44rpx; color: #333333;"
  />

  <!-- Tab切换 -->
  <view class="tab-container">
    <view class="tab-item {{activeTab === 'info' ? 'active' : ''}}" bindtap="switchTab" data-tab="info">
      设备信息
    </view>
    <view class="tab-item {{activeTab === 'records' ? 'active' : ''}}" bindtap="switchTab" data-tab="records">
      维修记录
    </view>
    <view class="tab-indicator" style="transform: translateX({{activeTab === 'info' ? '0' : '100%'}});"></view>
  </view>

  <!-- 设备信息内容 -->
  <view class="content-container" wx:if="{{activeTab === 'info'}}">
    <!-- 设备信息卡片（合并设备信息、备注、设备图片） -->
    <view class="info-card">
      <view class="card-title">设备信息</view>

      <view class="info-item">
        <view class="info-label">设备名称</view>
        <view class="info-value">{{deviceInfo.device_name || '海尔空调'}}</view>
      </view>

      <view class="info-item">
        <view class="info-label">设备SN码</view>
        <view class="info-value">{{deviceInfo.device_sn || 'HR384729484'}}</view>
      </view>

      <view class="info-item">
        <view class="info-label">设备类型</view>
        <view class="info-value">{{deviceInfo.device_type || '空调'}}</view>
      </view>

      <view class="info-item">
        <view class="info-label">保修年限</view>
        <view class="info-value">{{deviceInfo.repair_period || '2'}}年</view>
      </view>

      <!-- 备注信息 -->
      <view class="info-item" wx:if="{{deviceInfo.remarks}}">
        <view class="info-label">备注</view>
        <view class="info-value remark-content">{{deviceInfo.remarks}}</view>
      </view>

      <!-- 设备图片 -->
      <view class="info-item" wx:if="{{deviceInfo.device_images}}">
        <view class="info-label">设备图片</view>
        <view class="info-value">
          <view class="image-grid">
            <view
              class="image-item"
              bindtap="previewImage"
            >
              <image src="{{tool.cdn(deviceInfo.device_images)}}" class="device-image" mode="aspectFill" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户信息卡片 -->
    <view class="info-card">
      <view class="card-title">用户信息</view>
      
      <view class="user-profile">
        <view class="user-avatar">
          <image src="{{tool.cdn(deviceInfo.user_avatar) || 'https://service.infooi.cn/images/default-avatar.png'}}" class="avatar-image" />
        </view>
        <view class="user-details">
          <view class="user-name">{{deviceInfo.user_nickname || '用户'}}</view>
          <view class="user-phone" bindtap="callUser">{{deviceInfo.user_mobile || ''}}</view>
        </view>
      </view>
      
      <view class="info-item">
        <view class="info-label">真实姓名</view>
        <view class="info-value">{{deviceInfo.name || ''}}</view>
      </view>
    </view>
  </view>

  <!-- 维修记录内容 -->
  <view class="content-container" wx:if="{{activeTab === 'records'}}">
    <view class="records-timeline">
      <!-- 维修记录列表 -->
      <view class="record-timeline-item" wx:for="{{repairRecords}}" wx:key="id" wx:for-index="index">
        <!-- 时间轴节点 -->
        <view class="timeline-node">
          <view class="timeline-dot"></view>
          <view class="timeline-line" wx:if="{{index < repairRecords.length - 1}}"></view>
        </view>
        
        <!-- 记录卡片 -->
        <view class="record-card" bindtap="viewRecordDetail" data-id="{{item.id}}">
          <!-- 记录时间 -->
          <view class="record-time">{{item.createtime}}</view>
          
          <!-- 维修师傅信息 -->
          <view class="record-technician">
            <text class="technician-label">维修师傅：</text>
            <text class="technician-name">{{item.user_nickname}}</text>
          </view>
          
          <!-- 维修记录描述 -->
          <view class="record-description">
            <text class="description-label">维修记录：</text>
            <text class="description-content">{{item.remarks || '暂无备注'}}</text>
          </view>
          
          <!-- 维修图片 -->
          <view class="record-images" wx:if="{{item.images && item.images.length > 0}}">
            <view 
              class="record-image-item" 
              wx:for="{{item.images}}" 
              wx:key="imageIndex"
              wx:for-item="imageUrl"
              wx:for-index="imageIndex"
              bindtap="previewRecordImage"
              data-record-index="{{index}}"
              data-image-index="{{imageIndex}}"
            >
              <image src="{{tool.cdn(imageUrl)}}" class="record-image" mode="aspectFill" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{repairRecords.length === 0}}">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无维修记录</view>
        <view class="empty-desc">点击下方按钮添加第一条维修记录</view>
      </view>
    </view>
  </view>

  <!-- 底部添加维修记录按钮 -->
  <view class="bottom-button-container">
    <van-button 
      type="primary" 
      custom-class="add-record-button"
      bindtap="addRepairRecord"
    >
      添加维修记录
    </van-button>
  </view>
</view>