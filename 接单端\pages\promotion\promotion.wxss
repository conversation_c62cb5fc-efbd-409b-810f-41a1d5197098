.page {
  background-color: rgba(245,246,250,1.000000);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.block_1 {
  background: url(https://lanhu-oss-2537-2.lanhuapp.com/SketchPng374935bca07eb8a1f0ee5c274463f080c807c8aeb96b95a8188d600024a45e49) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  padding: 20rpx 0 263rpx 24rpx;
}
.box_6 {
  width: 678rpx;
  align-self: center;
  flex-direction: row;
  display: flex;
}
.text_1 {
  overflow-wrap: break-word;
  color: rgba(0,0,0,1);
  font-size: 34rpx;
  font-family: DINAlternate-Bold;
  font-weight: 700;
  text-align: right;
  white-space: nowrap;
  line-height: 40rpx;
  margin-top: 4rpx;
}
.image_1 {
  width: 33rpx;
  height: 20rpx;
  margin: 14rpx 0 14rpx 459rpx;
}
.label_1 {
  width: 40rpx;
  height: 40rpx;
  margin: 4rpx 0 4rpx 12rpx;
}
.label_2 {
  width: 48rpx;
  height: 48rpx;
  margin-left: 12rpx;
}
.box_7 {
  width: 419rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 42rpx 307rpx 0 0;
}
.label_5 {
  width: 44rpx;
  height: 44rpx;
}
.text_2 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 34rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: right;
  white-space: nowrap;
  line-height: 36rpx;
  margin-top: 4rpx;
}
.box_8 {
  width: 710rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 62rpx 0 0 16rpx;
}
.image-text_3 {
  width: 307rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.group_1 {
  box-shadow: 0px 3px 14px 0px rgba(0,0,0,0.100000);
  background-color: rgba(252,255,255,1.000000);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  padding: 5rpx 6rpx 6rpx 5rpx;
}
.section_4 {
  background-color: rgba(236,242,250,1.000000);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
}
.box_9 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ca997b3c580749f2bf8305b322abdf09_mergeImage.png);
  width: 89rpx;
  height: 89rpx;
  display: flex;
  flex-direction: column;
}
.text-group_6 {
  margin-bottom: 5rpx;
  display: flex;
  flex-direction: column;
}
.text_3 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 40rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 56rpx;
  margin-right: 67rpx;
}
.text_4 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-top: 13rpx;
}
.box_2 {
  background-color: rgba(0,0,0,0.200000);
  border-radius: NaNrpx;
  display: flex;
  flex-direction: row;
  margin: 22rpx 0 22rpx 0;
  padding: 15rpx 24rpx 15rpx 28rpx;
}
.image-text_4 {
  width: 114rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.label_6 {
  width: 24rpx;
  height: 24rpx;
  margin: 1rpx 0 1rpx 0;
}
.text-group_2 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}
.box_10 {
  display: flex;
  flex-direction: column;
  padding: 270rpx 24rpx 342rpx 24rpx;
}
.text_5 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 32rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: right;
  white-space: nowrap;
  line-height: 45rpx;
  margin-right: 574rpx;
}
.box_3 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  margin-top: 24rpx;
  display: flex;
  flex-direction: column;
  padding: 40rpx 85rpx 53rpx 35rpx;
}
.box_11 {
  flex-direction: row;
  display: flex;
}
.image_7 {
  width: 52rpx;
  height: 52rpx;
  margin: 5rpx 0 5rpx 0;
}
.text-group_7 {
  margin-left: 23rpx;
  display: flex;
  flex-direction: column;
}
.text_6 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-right: 13rpx;
}
.text_7 {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.image_8 {
  width: 52rpx;
  height: 51rpx;
  margin: 6rpx 0 5rpx 195rpx;
}
.text-group_8 {
  margin-left: 23rpx;
  display: flex;
  flex-direction: column;
}
.text_8 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-right: 16rpx;
}
.text_9 {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin-top: 12rpx;
}
.box_12 {
  width: 168rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 80rpx 403rpx 0 11rpx;
}
.image_9 {
  width: 32rpx;
  height: 56rpx;
  margin: 3rpx 0 3rpx 0;
}
.text-group_9 {
  display: flex;
  flex-direction: column;
}
.text_10 {
  overflow-wrap: break-word;
  color: rgba(51,51,51,1);
  font-size: 26rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}
.text_11 {
  overflow-wrap: break-word;
  color: rgba(153,153,153,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
  margin: 12rpx 51rpx 0 0;
}
.image_5 {
  width: 750rpx;
  height: 68rpx;
  margin-top: -1rpx;
}
.block_3 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  position: absolute;
  left: 24rpx;
  top: 356rpx;
  width: 702rpx;
  height: 453rpx;
  display: flex;
  flex-direction: column;
  padding: 40rpx 32rpx 32rpx 32rpx;
}
.text_12 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 26rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin-right: 508rpx;
}
.text-wrapper_5 {
  width: 227rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 24rpx 411rpx 0 0;
}
.text_13 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 28rpx;
  font-family: Helvetica-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin-top: 14rpx;
}
.text_14 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 46rpx;
  font-family: Helvetica-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 46rpx;
}
.section_5 {
  background-color: rgba(237,237,237,1.000000);
  width: 638rpx;
  height: 1rpx;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_6 {
  width: 638rpx;
  margin-top: 40rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_15 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.text_16 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.text_17 {
  overflow-wrap: break-word;
  color: rgba(102,102,102,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 24rpx;
}
.text-wrapper_7 {
  flex-direction: row;
  display: flex;
  margin: 20rpx 52rpx 0 0;
}
.text_18 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 32rpx;
  font-family: Helvetica;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
.text_19 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 32rpx;
  font-family: Helvetica;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 131rpx;
}
.text_20 {
  overflow-wrap: break-word;
  color: rgba(52,52,52,1);
  font-size: 32rpx;
  font-family: Helvetica;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 107rpx;
}
.text-wrapper_4 {
  background-color: rgba(23,130,250,1.000000);
  border-radius: 44rpx;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  padding: 24rpx 263rpx 24rpx 263rpx;
}
.text_21 {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 28rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
}
.image_6 {
  position: absolute;
  left: 285rpx;
  top: 3rpx;
  width: 414rpx;
  height: 187rpx;
}