/* 添加设备页面样式 - 按原始设计结构重构 */

/* 页面容器 */
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

/* 导航栏容器样式 */
.van-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

/* 导航栏元素样式 */
.van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

.van-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  box-sizing: border-box;
  /* 为底部用户信息卡片预留空间 */
}

/* 设备信息卡片 - 在上方 */
.device-info-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 用户信息卡片 - 固定在底部 */
.user-info-card {
  padding: 24rpx; 
  padding-top: 0rpx;
  margin-bottom: 100px;

}

/* 表单内容区域 */
.form-content {
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  z-index: 100;
}

/* 表单标题 */
.form-title {
  color: #323233;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 44rpx;
  margin-bottom: 24rpx;
}

/* van-cell 样式覆盖 - 解决问题1和问题2 */
.van-cell {
  padding: 24rpx 0 !important;
  border-bottom: 1rpx solid #f5f5f5 !important;
}

.van-cell:last-child {
  border-bottom: none !important;
}

.van-cell__title {
  color: #666666 !important;
  font-size: 30rpx !important;
  font-weight: normal !important;
  width: 131rpx !important;
  flex-shrink: 0 !important;
}

.van-cell__value {
  color: #cccccc !important;
  font-size: 30rpx !important;
  text-align: left !important;
}

/* van-field 在 cell 中的样式 */
.van-field {
  padding: 0 !important;
  background: transparent !important;
}

.van-field__input {
  font-size: 30rpx !important;
  color: #cccccc !important;
  text-align: left !important;
}

.van-field__placeholder {
  color: #cccccc !important;
}

/* 自定义cell标题样式 */
.cell-title {
  color: #666666;
  font-size: 30rpx;
  font-weight: normal;
  width: 131rpx;
  flex-shrink: 0;
}

/* 表单行样式 - 标题和输入框在一行 */
.form-item-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  box-sizing: border-box;
}

.form-item-row:last-child {
  border-bottom: none;
}

/* 自定义van-field样式 */
.custom-field {
  flex: 1;
  background: transparent !important;
  margin-left: 50rpx;
}

.custom-field .van-field__input {
  font-size: 30rpx !important;
  color: #cccccc !important;
  text-align: left !important;
  padding: 0 !important;
}

.custom-field .van-field__placeholder {
  color: #cccccc !important;
}

/* 选择字段样式 */
.select-field {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 50rpx;
  height: 48.7px;
}

.select-text {
  color: #cccccc;
  font-size: 30rpx;
  font-weight: normal;
  line-height: 40rpx;
}

.select-arrow-icon {
  width: 32rpx;
  height: 32rpx;
  color: #cccccc;
  flex-shrink: 0;
}

/* 选择字段有值时的样式 */
.form-item-row[data-selected="true"] .select-text {
  color: #333333;
}

/* Picker显示样式 */
.picker-display {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 50rpx;
  height: 48rpx;
  color: #cccccc;
  font-size: 30rpx;
}

.picker-display[data-selected="true"] {
  color: #333333;
}

picker {
  flex: 1;
  margin-left: 50rpx;
  padding: 24rpx 0;
}

/* 用户选择区域 */
.user-select-area {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 用户头像 */
.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f7f8fa;
  margin-right: 24rpx;
  flex-shrink: 0;
  overflow: hidden;
}

.user-avatar image {
  width: 100%;
  height: 100%;
}

/* 用户名称 */
.user-name {
  flex: 1;
  color: #333333;
  font-size: 28rpx;
  font-weight: normal;
  line-height: 40rpx;
}

/* 选择箭头 */
.select-arrow {
  width: 32rpx;
  height: 32rpx;
  color: #cccccc;
}

/* 表单项 */
.form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 32rpx;
  box-sizing: border-box;
}

.form-item:last-child {
  margin-bottom: 0;
}

/* 表单标签 */
.form-label {
  color: #666666;
  font-size: 30rpx;
  font-weight: normal;
  line-height: 40rpx;
  margin-bottom: 24rpx;
}

/* 图片上传容器 */
.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

/* 已选择的图片 */
.selected-image {
  position: relative;
  width: 152rpx;
  height: 152rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.device-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 图片删除按钮 */
.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图片上传区域 */
.upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9faf9;
  border-radius: 8rpx;
  padding: 28rpx;
  box-sizing: border-box;
  width: 152rpx;
  height: 152rpx;
}

.upload-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 8rpx;
  color: #999999;
}

.upload-text {
  color: #999999;
  font-size: 24rpx;
  font-weight: normal;
  text-align: center;
  line-height: 32rpx;
}

/* 备注输入区域 */
.remark-area {
  background-color: #f9faf9;
  border-radius: 10rpx;
  padding: 32rpx 24rpx 24rpx 32rpx;
  box-sizing: border-box;
  position: relative;
}

.remark-input {
  background: transparent;
  border: none;
  outline: none;
  width: 100%;
  min-height: 120rpx;
  font-size: 32rpx;
  color: #333333;
  line-height: 40rpx;
  resize: none;
}

.remark-input::placeholder {
  color: rgba(0, 0, 0, 0.3);
}

.remark-counter {
  position: absolute;
  bottom: 24rpx;
  right: 32rpx;
  color: rgba(0, 0, 0, 0.3);
  font-size: 24rpx;
  text-align: center;
  line-height: 40rpx;
}

/* 提交按钮包装器 */
.submit-button-wrapper {
  box-sizing: border-box;
  position: fixed;
  bottom: 0px;
  background-color: #ffffff;
  width: 100%;
  z-index: 1;
  padding:18rpx 32rpx;
}

/* 自定义按钮样式 - 解决问题4，增强优先级 */
.submit-button.van-button {
  background: #1782FA !important;
  border: none !important;
  border-radius: 38rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  color: #ffffff !important;
}

.submit-button.van-button.van-button--primary {
  background: #1782FA !important;
  border: none !important;
  border-radius: 58rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  color: #ffffff !important;
}

.submit-button.van-button--disabled {
  background: #f5f5f5 !important;
  color: #cccccc !important;
}

.submit-button.van-button--loading {
  background: #1782FA !important;
}

/* 通用van-button样式覆盖 */
.van-button--primary {
  background: #1782FA !important;
  border: none !important;
  border-radius: 38rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  height: 96rpx !important;
  line-height: 96rpx !important;
  color: #ffffff !important;
}

.van-button--disabled {
  background: #f5f5f5 !important;
  color: #cccccc !important;
}

/* 弹窗样式 */
.popup-content {
  background-color: #ffffff;
  border-radius: 16rpx 16rpx 0 0;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  box-sizing: border-box;
}

.popup-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 44rpx;
}

/* 选择器样式优化 */
.van-picker {
  background-color: #ffffff;
}

.van-picker__column {
  font-size: 28rpx;
  color: #333333;
}
.van-cell__title, .van-cell__value {
    /* flex: none !important; */
 
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .main-content {
    padding: 24rpx;
  }

  .form-title {
    font-size: 30rpx;
  }

  .van-cell__title {
    font-size: 28rpx !important;
  }

  .van-field__input {
    font-size: 28rpx !important;
  }

  .submit-button {
    font-size: 30rpx !important;
    height: 88rpx !important;
    line-height: 88rpx !important;
  }
}

/* 动画效果 */
.device-info-card,
.user-info-card {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹窗动画 */
.van-popup--bottom {
  animation: slideUpPopup 0.3s ease-out;
}

@keyframes slideUpPopup {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

/* 弹窗按钮样式 */
.popup-buttons {
  display: flex;
  padding: 32rpx;
  gap: 24rpx;
  background-color: #ffffff;
}

.popup-cancel-btn {
  flex: 1;
  background-color: #f5f5f5 !important;
  color: #666666 !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  font-size: 32rpx !important;
}

.popup-confirm-btn {
  flex: 1;
  background-color: #1989fa !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 12rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  font-size: 32rpx !important;
}

/* Picker确定按钮蓝色样式 */
picker {
  flex: 1;
  margin-left: 50rpx;
}

/* 全局picker样式 - 设置确定按钮为蓝色 */
.wx-picker-action-confirm {
  color: #1782FA !important;
}

/* 针对当前页面的picker样式 */
page .wx-picker-action-confirm {
  color: #1782FA !important;
}
.active-class{
  color: #1782FA !important;
}