// 分销订单页面
import http from '../../utils/http';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单列表
    orderList: [
      {
        id: 1,
        userName: '李钰',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 2,
        userName: '陈浩雨',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 3,
        userName: '王怡宜',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 4,
        userName: '徐秀华',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 5,
        userName: '苏亦梅',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 6,
        userName: '叶小海',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      }
    ],
    
    // 加载状态
    loading: false,
    hasMore: true,
    page: 1
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadOrderList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData();
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({ loading: true });
    
    const currentPage = Math.floor(this.data.orderList.length / 10) + 1;
    
    return http.get('user/inviteOrders', { page: currentPage, limit: 10 }, true, true)
      .then((res) => {
        console.log('加载更多邀请订单:', res);
        
        const newOrderList = res.data.map(item => ({
          id: item.id,
          userName: item.nickname || '未知用户',
          avatar: item.avatar ? (item.avatar.startsWith('http') ? item.avatar : 'https://service.infooi.cn' + item.avatar) : '',
          orderTime: item.createtime || '',
          orderAmount: '¥' + (item.order_amount || '0.00'),
          commission: item.commission || '0.00'
        }));
        
        this.setData({
          orderList: [...this.data.orderList, ...newOrderList],
          loading: false,
          hasMore: newOrderList.length >= 10 // 如果返回数据少于10条，说明没有更多数据
        });
        
        return res;
      })
      .catch((err) => {
        console.error('加载更多数据失败:', err);
        this.setData({ loading: false });
        
        // 接口失败时显示模拟数据
        const moreData = this.generateMockData(5);
        this.setData({
          orderList: [...this.data.orderList, ...moreData],
          hasMore: this.data.orderList.length < 20
        });
        
        throw err;
      });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '分销订单',
      path: '/pages/distribution/distribution'
    };
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  },

  /**
   * 加载订单数据
   */
  loadOrderData() {
    if (this.data.loading) return Promise.resolve();
    
    this.setData({ loading: true });
    
    return http.get('user/inviteOrders', {}, true, true)
      .then((res) => {
        console.log('邀请订单接口响应:', res);
        
        const orderList = res.data.map(item => ({
          id: item.id,
          userName: item.nickname || '未知用户',
          avatar: item.avatar ? (item.avatar.startsWith('http') ? item.avatar : 'https://service.infooi.cn' + item.avatar) : '',
          orderTime: item.createtime || '',
          orderAmount: '¥' + (item.order_amount || '0.00'),
          commission: item.commission || '0.00'
        }));
        
        this.setData({
          orderList: orderList,
          loading: false
        });
        
        return res;
      })
      .catch((err) => {
        console.error('邀请订单接口请求失败:', err);
        this.setData({ loading: false });
        
        // 接口失败时显示模拟数据
        const mockData = this.generateMockData();
        this.setData({
          orderList: mockData
        });
        
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
        
        throw err;
      });
  },

  /**
   * 加载订单列表
   */
  loadOrderList(showLoading = true) {
    if (this.data.loading) return Promise.resolve();
    
    this.setData({ loading: true });
    
    // 模拟接口调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = this.generateMockData();
        
        if (this.data.page === 1) {
          this.setData({
            orderList: mockData,
            loading: false
          });
        } else {
          this.setData({
            orderList: [...this.data.orderList, ...mockData],
            loading: false,
            hasMore: mockData.length > 0
          });
        }
        
        resolve();
      }, 1000);
    });
    
    // 实际接口调用代码（注释掉的示例）
    /*
    return http.get('distribution/orderList', {
      page: this.data.page,
      limit: 10
    }, showLoading, true)
      .then((res) => {
        const newData = res.data.list || [];
        
        if (this.data.page === 1) {
          this.setData({
            orderList: newData,
            loading: false
          });
        } else {
          this.setData({
            orderList: [...this.data.orderList, ...newData],
            loading: false,
            hasMore: newData.length > 0
          });
        }
        
        return res;
      })
      .catch((err) => {
        console.error('加载订单列表失败:', err);
        this.setData({ loading: false });
        throw err;
      });
    */
  },

  /**
   * 生成模拟数据
   */
  generateMockData() {
    const names = ['李钰', '陈浩雨', '王怡宜', '徐秀华', '苏亦梅', '叶小海', '张三', '李四', '王五', '赵六'];
    const mockData = [];
    
    for (let i = 0; i < 6; i++) {
      mockData.push({
        id: Date.now() + i,
        userName: names[Math.floor(Math.random() * names.length)],
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: (Math.random() * 200 + 50).toFixed(2),
        commission: (Math.random() * 100 + 10).toFixed(2)
      });
    }
    
    return mockData;
  }
});