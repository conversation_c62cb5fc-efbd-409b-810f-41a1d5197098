<!-- 选择用户页面 -->
<wxs src="../../../wxs/tool.wxs" module="tool"></wxs>
<view class="page page-bg">
  <!-- 导航栏 -->
  <van-nav-bar
    title="选择用户"
    left-arrow="{{true}}"
    arrow-direction="left"
    bind:click-left="goBack"
    background="transparent"
    border="{{false}}"
  />

  <!-- 搜索框 -->
  <view class="search-container">
    <van-search
      value="{{ searchValue }}"
      placeholder="搜索用户昵称或手机号"
      bind:search="onSearch"
      bind:change="onSearchChange"
      shape="round"
      background="#ffffff"
      left-icon=""
      show-action="{{false}}"
      custom-style="box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
    />
  </view>

  <!-- 用户列表 -->
  <view class="content">
    <!-- 搜索结果 -->
    <view class="user-list" wx:if="{{searchResults.length > 0}}">
      <view class="list-title">搜索结果</view>
      <view 
        class="user-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="selectUser"
        data-user="{{item}}"
      >
        <view class="user-avatar">
          <image 
            src="{{tool.cdn(item.avatar) || '/static/service/default-avatar.png'}}" 
            mode="aspectFill"
            class="avatar-image"
          />
        </view>
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="user-phone">{{item.phone}}</text>
        </view>
        <view class="select-status">
          <van-icon 
            name="{{selectedUserId === item.id ? 'checked' : 'circle'}}" 
            size="20px" 
            color="{{selectedUserId === item.id ? '#1782FA' : '#CCCCCC'}}"
          />
        </view>
      </view>
    </view>

    <!-- 推荐用户 -->
    <view class="user-list" wx:if="{{recommendUsers.length > 0}}">
      <view 
        class="user-item" 
        wx:for="{{recommendUsers}}" 
        wx:key="id"
        bindtap="selectUser"
        data-user="{{item}}"
      >
        <view class="user-avatar">
          <image 
            src="{{tool.cdn(item.avatar) || '/static/service/default-avatar.png'}}" 
            mode="aspectFill"
            class="avatar-image"
          />
        </view>
        <view class="user-info">
          <text class="user-name">{{item.name}}</text>
          <text class="user-phone">{{item.phone}}</text>
        </view>
        <view class="select-status">
          <van-icon 
            name="{{selectedUserId === item.id ? 'checked' : 'circle'}}" 
            size="20px" 
            color="{{selectedUserId === item.id ? '#1782FA' : '#CCCCCC'}}"
          />
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <van-empty 
      wx:if="{{!loading && searchValue && searchResults.length === 0}}"
      image="search" 
      description="未找到相关用户"
    />
    
    <van-empty 
      wx:if="{{!loading && !searchValue && recommendUsers.length === 0}}"
      image="default" 
      description="暂无推荐用户"
    />
  </view>

  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" size="24px" vertical>
    加载中...
  </van-loading>
</view>