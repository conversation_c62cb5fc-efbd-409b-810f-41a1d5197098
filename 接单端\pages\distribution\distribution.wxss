/* 引入通用样式 */
@import "/common.wxss";

/* 自定义导航栏样式 */
.custom-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

.custom-nav-bar .van-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

.custom-nav-bar .van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

/* 页面样式 */
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 订单列表容器 */
.order-list {
  padding: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 订单项 */
.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 头像容器 */
.avatar-container {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  background-color: #d8d8d8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.avatar-default {
  width: 100%;
  height: 100%;
  background-color: #d8d8d8;
  border-radius: 50%;
}

/* 用户名 */
.user-name {
  color: #333333;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
}

/* 订单时间 */
.order-time {
  color: #999999;
  font-size: 24rpx;
  line-height: 40rpx;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #e6e6e6;
  margin-bottom: 24rpx;
}

/* 订单详情 */
.order-details {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

/* 金额信息 */
.amount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

/* 佣金信息 */
.commission-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  align-items: flex-end;
}

/* 标签样式 */
.label {
  color: #999999;
  font-size: 22rpx;
  line-height: 22rpx;
}

/* 订单金额 */
.amount {
  color: #666666;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
}

/* 佣金金额 */
.commission {
  color: #e60817;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
}