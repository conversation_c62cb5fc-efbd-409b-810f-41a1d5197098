/* 添加维修记录页面样式 */

/* 表单容器 */
.form-container {
  padding: 0 24rpx;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 表单卡片 */
.form-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border: 2rpx solid #f0f0f0;
}

.form-content {
  display: flex;
  flex-direction: column;
}

/* 表单区块 */
.form-section {
  margin-bottom: 32rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 30rpx;
  color: #666666;
  margin-bottom: 24rpx;
  font-weight: normal;
}

/* 备注输入区域 */
.textarea-container {
  position: relative;
  background: #f9faf9;
  border-radius: 10rpx;
  padding: 32rpx 24rpx 24rpx 32rpx;
  min-height: 200rpx;
}

.remark-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
  background: transparent;
  border: none;
  outline: none;
}

.remark-textarea::placeholder {
  color: rgba(0, 0, 0, 0.3);
}

.char-counter {
  position: absolute;
  bottom: 24rpx;
  right: 24rpx;
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.3);
  line-height: 40rpx;
}

/* 图片上传区域 */
.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 186rpx;
  height: 186rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.image-delete {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-image-btn {
  width: 186rpx;
  height: 186rpx;
  background: #f9faf9;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #e0e0e0;
}

.add-image-icon {
  width: 56rpx;
  height: 56rpx;
}

.add-image-text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 8rpx;
  line-height: 32rpx;
}


/* 底部提交按钮 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 18rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}


/* 导航栏样式 */
.van-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

.van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

.van-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .form-card {
    padding: 24rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
  
  .remark-textarea {
    font-size: 30rpx;
  }
  
  .image-item,
  .add-image-btn {
    width: calc(33.333% - 12rpx);
    height: 160rpx;
  }
}

/* 自定义提交按钮样式 */
.custom-submit-button {
  width: 100%;
  height: 96rpx;
  background: #1782FA;
  border-radius: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.custom-submit-button:active {
  background: #1565c0;
  transform: scale(0.98);
}

.custom-submit-button.disabled {
  background: #f5f5f5;
  color: #cccccc;
  pointer-events: none;
}

.submit-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 45rpx;
}

.custom-submit-button.disabled .submit-text {
  color: #cccccc;
}

/* 加载状态 */
.van-button--loading .van-loading {
  margin-right: 16rpx !important;
}
