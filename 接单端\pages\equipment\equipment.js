const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"

Page({
  /**
   * 页面的初始数据
   */
  data: {
    deviceList: [],
    loading: false,
    searchKeyword: '',
    safeAreaTop: 44 // 安全区域高度
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备管理页面加载', options);
    
    // 计算安全区域高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44;
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
    const safeAreaTop = statusBarHeight + menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2;
    
    this.setData({
      safeAreaTop: safeAreaTop
    });
    
    this.loadDeviceList();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('设备管理页面渲染完成');
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('设备管理页面显示');
    this.loadDeviceList();
  },

  /**
   * 加载设备列表
   */
  loadDeviceList() {
    this.setData({ loading: true });
    
    const params = {};
    if (this.data.searchKeyword) {
      params.q = this.data.searchKeyword;
    }

    http.get('device/deviceList', params).then(res => {
      this.setData({
        deviceList: res.data || [],
        loading: false
      });
    }).catch(err => {
      console.error('加载设备列表失败:', err);
      util.toast('加载失败，请重试');
      this.setData({ loading: false });
    });
  },

  /**
   * 搜索输入变化
   */
  onSearchInput(e) {
    const keyword = e.detail.value || e.detail
    this.setData({
      searchKeyword: keyword
    });

    // 实时搜索（可选，如果不需要实时搜索可以注释掉）
    // this.debounceSearch();
  },

  /**
   * 执行搜索
   */
  onSearch(e) {
    const keyword = e.detail.value || e.detail || this.data.searchKeyword
    console.log('执行搜索，关键词：', keyword)

    this.setData({
      searchKeyword: keyword
    });

    this.loadDeviceList();
  },

  /**
   * 清空搜索
   */
  onSearchClear() {
    console.log('清空搜索')
    this.setData({
      searchKeyword: ''
    });
    this.loadDeviceList();
  },

  /**
   * 防抖搜索（实时搜索用）
   */
  debounceSearch() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }

    this.searchTimer = setTimeout(() => {
      this.loadDeviceList()
    }, 500) // 500ms延迟
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    console.log('设备管理页面隐藏');
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log('设备管理页面卸载');

    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
      this.searchTimer = null
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.loadDeviceList();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底');
    wx.showToast({
      title: '没有更多数据',
      icon: 'none'
    });
  },

  /**
   * 设备项点击事件
   */
  onDeviceItemTap(e) {
    const device = e.currentTarget.dataset.device
    console.log('点击设备项，设备：', device)
    
    if (!device) {
      util.toast('设备信息错误')
      return
    }
    
    wx.navigateTo({
      url: `/pages/equipment/detail/detail?id=${device.id}`,
      success: () => {
        console.log('跳转到设备详情页面成功')
      },
      fail: (err) => {
        console.error('跳转到设备详情页面失败', err);
        util.toast('页面跳转失败');
      }
    });
  },

  /**
   * 添加记录按钮点击事件
   */
  onAddRecordTap(e) {
    const device = e.currentTarget.dataset.device
    console.log('添加记录，设备：', device)
    
    // 阻止事件冒泡，避免触发设备详情
    if (e.stopPropagation) {
      e.stopPropagation()
    }
    
    if (!device) {
      util.toast('设备信息错误')
      return
    }
    
    wx.navigateTo({
      url: `/pages/record/add/add?deviceId=${device.id}&deviceName=${encodeURIComponent(device.device_name)}&deviceSn=${device.device_sn}`,
      success: () => {
        console.log('跳转到添加记录页面成功')
      },
      fail: (err) => {
        console.error('跳转到添加记录页面失败：', err)
        util.toast('跳转失败')
      }
    })
  },

  /**
   * 点击添加新设备按钮
   */
  onAddDeviceTap() {
    console.log('添加新设备');
    
    // 跳转到添加设备页面
    wx.navigateTo({
      url: '/pages/equipment/add/add',
      success: () => {
        console.log('跳转到添加设备页面成功');
      },
      fail: (err) => {
        console.error('跳转到添加设备页面失败', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 搜索框点击事件
   */
  onSearchTap() {
    // 可以在这里添加搜索框获取焦点等逻辑
    console.log('点击搜索框');
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  }
});