<!-- 添加维修记录页面 -->
<view class="page-bg">
  <!-- 自定义导航栏 -->
  <van-nav-bar 
    title="添加维修记录" 
    left-arrow="{{true}}"
    arrow-direction="left"
    bind:click-left="onClickLeft"
    custom-style="background: transparent; border-bottom: none;"
    title-style="color: #333333; font-weight: 500;"
    left-arrow-style="font-size: 44rpx; color: #333333;"
  />

  <!-- 表单内容 -->
  <view class="form-container">
    <view class="form-card">
      <view class="form-content">
        <!-- 维修备注 -->
        <view class="form-section">
          <view class="section-title">维修备注</view>
          <view class="textarea-container">
            <textarea
              class="remark-textarea"
              placeholder="请输入备注"
              value="{{formData.remark}}"
              maxlength="500"
              auto-height
              bind:input="onRemarkInput"
              bind:focus="onRemarkFocus"
              bind:blur="onRemarkBlur"
            />
            <view class="char-counter">{{remarkLength}} / 500</view>
          </view>
        </view>

        <!-- 维修照片 -->
        <view class="form-section">
          <view class="section-title">维修照片</view>
          <view class="image-upload-container">
            <!-- 已选择的图片 -->
            <view
              class="image-item"
              wx:for="{{formData.imagesArray}}"
              wx:key="index"
            >
              <image src="{{item}}" class="uploaded-image" mode="aspectFill" />
              <view class="image-delete" bindtap="deleteImage" data-index="{{index}}">
                <van-icon name="cross" size="24rpx" color="#ffffff" />
              </view>
            </view>
            
            <!-- 添加图片按钮 -->
            <view 
              class="add-image-btn" 
              bindtap="chooseImage"
              wx:if="{{formData.images.length < 9}}"
            >
              <van-icon name="plus" size="56rpx" color="#999999" />
              <text class="add-image-text">添加图片</text>
            </view>
          </view>
          <view class="image-tip">最多可上传9张图片</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部提交按钮 -->
  <view class="submit-container">
    <view 
      class="custom-submit-button {{!canSubmit || submitting ? 'disabled' : ''}}"
      bindtap="submitRecord"
    >
      <van-loading wx:if="{{submitting}}" size="32rpx" color="#ffffff" />
      <text class="submit-text">{{submitting ? '提交中...' : '确定添加'}}</text>
    </view>
  </view>
</view>