// pages/my/my.js
const app = getApp()
import http from "../../utils/http"
import util from "../../utils/util"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    moreList:[
      {img:'../../static/index/my/my-5.png',title:'加入商家',url:'/service/merchant/merchant'},
      {img:'../../static/index/my/my-7.png',title:'提现绑定',url:'/service/account/account'},
      {img:'../../static/index/my/my-8.png',title:'平台公告',url:'/service/notice/notice'},
      {img:'../../static/index/my/my-9.png',title:'我的评价',url:'/service/comments/comments'},
      {img:'../../static/index/my/my-10.png',title:'培训中心',url: '/service/exercise/exercise'},
      {img:'../../static/index/my/my-11.png',title:'联系客服'},
      {img:'../../static/index/my/my-12.png',title:'意见反馈',url:'/service/feedback/feedback'},
    ],
    info: '',
    config: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取系统信息，计算自定义导航栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 44; // 状态栏高度，默认44px
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算顶部安全区域高度：状态栏 + 胶囊按钮区域
    const safeAreaTop = statusBarHeight + menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2;

    this.setData({
      safeAreaTop: safeAreaTop
    });

    // 加载配置信息
    this.onLoadConfig();
  },
  onLoadConfig(){
    this.setData({
      config: app.globalData.config
    })
  },
  toserve(){
    util.authSkip('/service/serveList/serveList')
  },
  // 跳转信用
  tocredit(){
    util.authSkip('/service/credit/credit')
  },
  // 跳转余额
  toBalance(e){
    util.authSkip('/service/balance/balance?type='+e.currentTarget.dataset.type)
  },
  // 跳转提现
  toWithdraw(){
    util.authSkip('/service/balance/balance?type=0')
  },
  // 跳转保证金
  toEarnestMoney(){
    util.authSkip('/service/earnestMoney/earnestMoney')
  },
  toSet(){
    util.authSkip('/service/set/set')
  },
  toLogin(){
    util.skip('/service/login/login')
  },

  // 跳转到个人信息页面
  toProfile(){
    console.log('点击头像，准备跳转到个人信息页面');
    console.log('当前用户信息：', this.data.info);

    if(!this.data.info) {
      console.log('用户信息为空，跳转到登录页面');
      util.skip('/service/login/login');
      return;
    }

    console.log('用户已登录，跳转到个人信息页面');
    util.authSkip('/service/userInfo/userInfo');
  },
  toServel(e){
    util.authSkip(e.currentTarget.dataset.url)
  },

  // 服务功能区域事件处理
  toServiceArea(){
    // 跳转到服务项目列表页面
    util.authSkip('/service/serveList/serveList')
  },

  toServiceCategory(){
    // 跳转到服务项目分类页面
    util.authSkip('/service/serveItem/serveItem')
  },

  toIncomeDetail(){
    // 跳转到收入页面（已有的收入统计页面）
    util.authSkip('/pages/income/income')
  },

  toGoodReviews(){
    // 跳转到我的评价页面，需要传递当前用户的skill_id
    if(this.data.info && this.data.info.skill && this.data.info.skill.id) {
      util.authSkip('/service/comments/comments?type=skill&id=' + this.data.info.skill.id)
    } else {
      util.toast('请先完善个人信息')
    }
  },

  toDistribution(){
    // 跳转到分销推广页面
    util.authSkip('/pages/promotion/promotion')
  },

  // 退出登录功能
  logout(){
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的用户信息
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');

          // 重置页面数据
          this.setData({
            info: ''
          });

          // 跳转到登录页面
          wx.reLaunch({
            url: '/service/login/login'
          });

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },
  getInfo(){
    console.log('开始获取用户信息');
    const token = wx.getStorageSync('token');
    console.log('当前token：', token);

    if(!token) {
      console.log('没有token，不获取用户信息');
      return;
    }

    http.get('skill/skillindex','',!this.data.info).then(res => {
      console.log('获取用户信息成功：', res.data);
      this.data.moreList[3].url = '/service/comments/comments?type=skill&id='+res.data.skill.id
      this.setData({
        info: res.data,
        moreList: this.data.moreList
      })
    }).catch(err => {
      console.log('获取用户信息失败：', err);
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getInfo()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})