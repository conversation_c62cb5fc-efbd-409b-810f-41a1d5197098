<wxs src="../../wxs/tool.wxs" module="tool"></wxs>
<view class="page page-bg">
  <!-- 导航栏 -->
  <van-nav-bar 
    title="设备管理" 
    bind:click-left="onClickLeft"
    custom-style="background: transparent; border-bottom: none;" 
    title-style="color: #333333; font-weight: 500;"
    left-arrow-style="font-size: 44rpx; color: #333333;"
  />
  
  <!-- 头部区域 -->
  <view class="header-section">
    <!-- 搜索框 -->
    <view class="search-wrapper">
      <text class="search-placeholder">搜索用户姓名或手机号</text>
    </view>
  </view>
  
  <!-- 设备列表区域 -->
  <view class="content-section">
    <view class="device-list">
      <!-- 设备卡片循环渲染 -->
      <view class="device-item" wx:for="{{deviceList}}" wx:key="id" bindtap="onDeviceItemTap" data-device="{{item}}">
        <view class="device-content">
          <!-- 设备图片 -->
          <view class="device-image" style="background-image: url({{tool.cdn(item.device_images)}})"></view>
          <!-- 设备信息 -->
          <view class="device-info">
            <text class="device-name">{{item.device_name}}</text>
            <text class="device-sn" decode="true">SN码&nbsp;{{item.device_sn}}</text>
            <text class="device-user" decode="true">用户信息：{{item.name}}｜{{item.mobile}}</text>
            <view class="record-info">
              <text class="record-count">{{item.maintenance_count || 0}}</text>
              <text class="record-separator">/</text>
              <text class="record-text">条维修记录</text>
            </view>
          </view>
        </view>
        <!-- 添加记录按钮 -->
        <view class="add-record-btn" bindtap="onAddRecordTap" data-device="{{item}}">
          <text class="btn-text">添加记录</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部固定按钮 -->
  <view class="fixed-bottom-btn" bindtap="onAddDeviceTap">
    <view class="btn-wrapper">
      <text class="btn-text">添加新设备</text>
    </view>
  </view>
</view>
