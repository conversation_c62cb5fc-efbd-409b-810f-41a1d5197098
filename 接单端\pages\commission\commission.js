// 佣金明细页面
import http from '../../utils/http';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 筛选条件
    selectedMonth: '2025年04月',
    selectedType: '全部',
    
    // 佣金记录列表
    commissionList: [],
    
    // 分页信息
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    
    // 筛选选项
    monthOptions: [
      '2025年04月',
      '2025年03月', 
      '2025年02月',
      '2025年01月'
    ],
    typeOptions: [
      '全部',
      '分销佣金',
      '佣金提现'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 先加载模拟数据
    this.loadMockData();
    // 然后尝试加载真实数据
    this.loadCommissionData();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '佣金明细',
      path: '/pages/commission/commission'
    };
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  },

  /**
   * 月份选择器点击事件
   */
  onMonthSelectorTap() {
    wx.showActionSheet({
      itemList: this.data.monthOptions,
      success: (res) => {
        const selectedMonth = this.data.monthOptions[res.tapIndex];
        this.setData({
          selectedMonth: selectedMonth
        });
        this.refreshData();
      }
    });
  },

  /**
   * 类型选择器点击事件
   */
  onTypeSelectorTap() {
    wx.showActionSheet({
      itemList: this.data.typeOptions,
      success: (res) => {
        const selectedType = this.data.typeOptions[res.tapIndex];
        this.setData({
          selectedType: selectedType
        });
        this.refreshData();
      }
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      commissionList: [],
      currentPage: 1,
      hasMore: true
    });
    return this.loadCommissionData();
  },

  /**
   * 加载佣金数据
   */
  loadCommissionData() {
    if (this.data.loading) return Promise.resolve();
    
    this.setData({ loading: true });
    
    const params = {
      page: this.data.currentPage,
      limit: this.data.pageSize,
      month: this.data.selectedMonth,
      type: this.data.selectedType === '全部' ? '' : this.data.selectedType
    };
    
    return http.get('/api/service/user/commissionDetail', params, true, true)
      .then((res) => {
        console.log('佣金明细接口响应:', res);
        
        const newList = res.data.map(item => ({
          id: item.id,
          type: item.type || '分销佣金',
          createTime: item.createtime || '',
          amount: parseFloat(item.amount || 0),
          balanceType: item.balance_type || '佣金',
          balance: item.balance || '0.00'
        }));
        
        this.setData({
          commissionList: this.data.currentPage === 1 ? newList : [...this.data.commissionList, ...newList],
          hasMore: newList.length >= this.data.pageSize,
          loading: false
        });
        
        return res;
      })
      .catch((err) => {
        console.error('佣金明细接口请求失败:', err);
        this.setData({ loading: false });
        
        // 接口失败时显示模拟数据
        const mockData = this.generateMockData();
        this.setData({
          commissionList: this.data.currentPage === 1 ? mockData : [...this.data.commissionList, ...mockData],
          hasMore: mockData.length >= this.data.pageSize
        });
        
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
        
        throw err;
      });
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    
    this.loadCommissionData();
  },

  /**
   * 加载模拟数据
   */
  loadMockData() {
    const mockData = this.generateMockData(15); // 生成15条模拟数据
    this.setData({
      commissionList: mockData,
      hasMore: true,
      loading: false
    });
  },

  /**
   * 生成模拟数据
   */
  generateMockData(count = 10) {
    const types = ['分销佣金', '佣金提现'];
    const times = [
      '2025.04.16 19:23',
      '2025.04.15 14:30',
      '2025.04.14 09:15',
      '2025.04.13 16:45',
      '2025.04.12 11:20',
      '2025.04.11 08:30',
      '2025.04.10 20:15',
      '2025.04.09 13:40',
      '2025.04.08 17:25',
      '2025.04.07 10:50'
    ];
    
    const mockData = [];
    
    for (let i = 0; i < count; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const isPositive = type === '分销佣金';
      const timeIndex = i % times.length;
      
      mockData.push({
        id: Date.now() + i,
        type: type,
        createTime: times[timeIndex],
        amount: isPositive ? 
          parseFloat((Math.random() * 200 + 10).toFixed(2)) : 
          -parseFloat((Math.random() * 100 + 10).toFixed(0)),
        balanceType: type === '分销佣金' ? '佣金' : (Math.random() > 0.5 ? '佣金' : '余额'),
        balance: (Math.random() * 5000 + 100).toFixed(2)
      });
    }
    
    return mockData;
  }
});