/* 引入通用样式 */
@import "/common.wxss";

/* 自定义导航栏样式 */
.custom-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

.custom-nav-bar .van-nav-bar__title {
  color: #333333 !important;
  font-weight: 600 !important;
}

.custom-nav-bar .van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

/* 页面样式 */
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 筛选区域 */
.filter-section {
  background-color: #ffffff;
  padding: 21rpx 32rpx 19rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 月份选择器 */
.month-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.month-text {
  color: #333333;
  font-size: 32rpx;
  font-weight: 500;
  line-height: 56rpx;
}

/* 类型选择器 */
.type-selector {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-text {
  color: #323233;
  font-size: 28rpx;
  line-height: 28rpx;
}

.arrow-icon {
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #f5f5f5;
}

/* 佣金记录列表 */
.commission-list {
  background-color: #ffffff;
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
}

/* 佣金记录项 */
.commission-item {
  padding: 40rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.commission-item:last-child {
  border-bottom: none;
}

/* 记录信息 */
.record-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

/* 记录左侧信息 */
.record-left {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.record-type {
  color: #333333;
  font-size: 32rpx;
  font-weight: 400;
  line-height: 56rpx;
}

.record-time {
  color: #999999;
  font-size: 24rpx;
  line-height: 32rpx;
}

/* 记录右侧信息 */
.record-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
}

/* 金额样式 */
.amount {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 56rpx;
}

.amount-positive {
  color: #333333;
}

.amount-negative {
  color: #1782fa;
}

/* 余额信息 */
.balance-info {
  color: #999999;
  font-size: 24rpx;
  line-height: 32rpx;
}

/* 加载更多提示 */
.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.load-more-text {
  color: #999999;
  font-size: 28rpx;
}

/* 加载中提示 */
.loading {
  padding: 40rpx 0;
  text-align: center;
}

.loading-text {
  color: #999999;
  font-size: 28rpx;
}

/* 没有更多数据提示 */
.no-more {
  padding: 40rpx 0;
  text-align: center;
}

.no-more-text {
  color: #999999;
  font-size: 28rpx;
}