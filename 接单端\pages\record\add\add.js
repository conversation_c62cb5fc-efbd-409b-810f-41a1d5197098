// 添加维修记录页面
const app = getApp()
import http from "../../../utils/http"
import util from "../../../utils/util"

Page({
  data: {
    // 设备信息（从上一页传递）
    deviceInfo: {
      id: '',
      name: '',
      sn: ''
    },
    
    // 表单数据 - 严格按照接口文档参数
    formData: {
      device_id: '',    // 设备ID
      remarks: '',      // 备注
      images: '',       // 维修记录图片（字符串格式，用于提交）
      imagesArray: []   // 维修记录图片（数组格式，用于显示）
    },
    
    // 备注字符长度
    remarkLength: 0,
    
    // 提交状态
    submitting: false,
    
    // 是否可以提交
    canSubmit: false,
    
    // 选择的图片列表（用于UI显示）
    selectedImages: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('添加维修记录页面加载，参数：', options)
    
    // 获取设备信息
    if (options.deviceId) {
      this.setData({
        'deviceInfo.id': options.deviceId,
        'formData.device_id': options.deviceId
      })
    }
    
    if (options.deviceName) {
      this.setData({
        'deviceInfo.name': decodeURIComponent(options.deviceName)
      })
    }
    
    if (options.deviceSn) {
      this.setData({
        'deviceInfo.sn': options.deviceSn
      })
    }
    
    console.log('设备信息：', this.data.deviceInfo)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('添加维修记录页面渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    wx.stopPullDownRefresh()
  },

  /**
   * 备注输入事件
   */
  onRemarkInput(event) {
    const value = event.detail.value
    const length = value.length
    
    this.setData({
      'formData.remarks': value,
      remarkLength: length
    })
    
    // 检查是否可以提交
    this.checkCanSubmit()
  },

  /**
   * 返回按钮点击事件
   */
  onClickLeft() {
    // 检查是否有未保存的内容 - 修复字段名错误和空值检查
    const remarks = this.data.formData.remarks || ''
    const images = this.data.formData.images || ''

    if (remarks.trim() || images.length > 0) {
      wx.showModal({
        title: '提示',
        content: '当前有未保存的内容，确定要离开吗？',
        success: (res) => {
          if (res.confirm) {
            this.goBack()
          }
        }
      })
    } else {
      this.goBack()
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      success: () => {
        console.log('返回成功')
      },
      fail: (err) => {
        console.error('返回失败：', err)
        wx.showToast({
          title: '返回失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 备注输入事件
   */
  onRemarkInput(event) {
    const value = event.detail.value
    const length = value.length
    
    this.setData({
      'formData.remarks': value,
      remarkLength: length
    })
    
    // 检查是否可以提交
    this.checkCanSubmit()
  },

  /**
   * 备注聚焦事件
   */
  onRemarkFocus(event) {
    console.log('备注输入框聚焦')
  },

  /**
   * 备注失焦事件
   */
  onRemarkBlur(event) {
    console.log('备注输入框失焦')
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const currentCount = this.data.selectedImages.length
    const maxCount = 9 - currentCount
    
    if (maxCount <= 0) {
      util.toast('图片已达上限')
      return
    }
    
    wx.chooseMedia({
      count: maxCount,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        console.log('选择图片成功：', res)
        
        const tempFiles = res.tempFiles
        this.uploadImages(tempFiles)
      },
      fail: (err) => {
        console.error('选择图片失败：', err)
        if (err.errMsg !== 'chooseMedia:fail cancel') {
          util.toast('选择图片失败')
        }
      }
    })
  },

  /**
   * 批量上传图片
   */
  uploadImages(tempFiles) {
    wx.showLoading({
      title: '上传中...'
    })
    
    const uploadPromises = tempFiles.map(file => {
      return http.upload(file.tempFilePath, {
        type: 'image'
      }, false, false)
    })
    
    Promise.all(uploadPromises).then(results => {
      console.log('图片上传成功：', results)

      const newImages = results.map((result, index) => ({
        localPath: tempFiles[index].tempFilePath,
        serverPath: result.data.fullurl // 修复：使用完整的图片URL
      }))

      this.setData({
        selectedImages: [...this.data.selectedImages, ...newImages]
      })
      
      // 更新表单数据中的图片字段
      this.updateImagesField()
      
      // 检查是否可以提交
      this.checkCanSubmit()
      
      wx.hideLoading()
      util.toast(`上传成功${results.length}张图片`)
    }).catch(err => {
      console.error('图片上传失败:', err)
      wx.hideLoading()
      util.toast('图片上传失败，请重试')
    })
  },

  /**
   * 删除图片
   */
  deleteImage(event) {
    const index = event.currentTarget.dataset.index
    console.log('删除图片，索引：', index)
    
    wx.showModal({
      title: '提示',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          const images = [...this.data.selectedImages]
          images.splice(index, 1)
          
          this.setData({
            selectedImages: images
          })
          
          // 更新表单数据中的图片字段
          this.updateImagesField()
          
          // 检查是否可以提交
          this.checkCanSubmit()
          
          util.toast('删除成功')
        }
      }
    })
  },

  /**
   * 更新图片字段
   */
  updateImagesField() {
    // 获取服务器路径数组（用于显示）
    const serverPathsArray = this.data.selectedImages
      .map(img => img.serverPath)
      .filter(path => path)

    // 获取服务器路径字符串（用于提交）
    const serverPathsString = serverPathsArray.join(',')

    this.setData({
      'formData.images': serverPathsString,
      'formData.imagesArray': serverPathsArray // 添加数组格式用于WXML显示
    })
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { remarks, images } = this.data.formData
    const canSubmit = remarks.trim().length > 0 || this.data.selectedImages.length > 0
    
    this.setData({
      canSubmit
    })
  },

  /**
   * 提交维修记录
   */
  submitRecord() {
    if (this.data.submitting) {
      return
    }
    
    const { remarks, images } = this.data.formData
    
    // 表单验证
    if (!remarks.trim() && !images) {
      util.toast('请填写维修备注或上传图片')
      return
    }
    
    console.log('提交维修记录：', this.data.formData)
    
    this.setData({
      submitting: true
    })
    
    http.post('device/maintenanceAdd', this.data.formData).then(res => {
      console.log('添加维修记录成功：', res)
      util.toast('添加成功')
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }).catch(err => {
      console.error('添加维修记录失败:', err)
      util.toast(err.msg || '添加失败，请重试')
    }).finally(() => {
      this.setData({ submitting: false })
    })
  }
})