// pages/equipment/add/add.js
const util = require('../../../utils/util')
import http from '../../../utils/http'

Page({
  data: {
    safeAreaTop: 44, // 安全区域高度
    submitting: false, // 提交状态
    
    // 表单数据
    formData: {
      device_name: '',
      device_sn: '',
      device_type: '',
      repair_period: '',
      device_images: '', // 单张图片
      name: '',
      remarks: ''
    },
    
    // 计算属性
    remarkLength: 0,
    
    // 图片显示
    selectedImage: '', // 用于显示选中的图片
    
    // 用户信息显示
    userInfo: '',
    userAvatar: '',
    
    // 设备类型选择器
    typeColumns: ['空调', '热水器', '洗衣机', '冰箱', '电视', '其他'],
    selectedTypeIndex: -1,
    
    // 保修年限选择器
    warrantyColumns: ['1', '2', '3', '5', '10'],
    selectedWarrantyIndex: -1,
    
    // 日期选择器
    showDatePopup: false,
    currentDate: new Date().getTime(),
    minDate: new Date('2000-01-01').getTime(),
    maxDate: new Date().getTime()
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.calculateSafeArea()
  },

  /**
   * 计算安全区域高度
   */
  calculateSafeArea() {
    const systemInfo = wx.getSystemInfoSync()
    const statusBarHeight = systemInfo.statusBarHeight || 44
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
    
    const safeAreaTop = statusBarHeight + menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2
    
    this.setData({
      safeAreaTop: safeAreaTop
    })
  },

  /**
   * 表单字段变化
   */
  onFieldChange(event) {
    const field = event.currentTarget.dataset.field
    const value = event.detail.value !== undefined ? event.detail.value : event.detail
    
    const updateData = {
      [`formData.${field}`]: value
    }
    
    // 如果是备注字段，同时更新字符长度
    if (field === 'remarks') {
      updateData.remarkLength = (value || '').length
    }
    
    this.setData(updateData)
  },

  /**
   * 设备类型选择变化
   */
  onTypeChange(event) {
    const index = event.detail.value
    const selectedType = this.data.typeColumns[index]
    this.setData({
      selectedTypeIndex: index,
      'formData.device_type': selectedType
    })
  },

  /**
   * 保修年限选择变化
   */
  onWarrantyChange(event) {
    const index = event.detail.value
    const selectedWarranty = this.data.warrantyColumns[index]
    this.setData({
      selectedWarrantyIndex: index,
      'formData.repair_period': selectedWarranty
    })
  },

  /**
   * 选择用户
   */
  selectUser() {
    wx.navigateTo({
      url: '/pages/equipment/selectUser/selectUser',
      events: {
        // 监听用户选择结果
        selectUser: (data) => {
          this.setData({
            userInfo: `${data.name}｜${data.phone}`,
            'formData.name': data.name,
            userAvatar: data.avatar || ''
          })
        }
      }
    })
  },

  /**
   * 选择图片
   */
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]

        // 上传图片到服务器
        wx.showLoading({
          title: '上传中...'
        })

        http.upload(tempFilePath, {
          type: 'image'
        }, false, false).then(result => {
          console.log('图片上传成功：', result)

          // 验证返回数据结构
          if (!result || !result.data || !result.data.fullurl) {
            throw new Error('服务器返回数据格式错误')
          }

          // 使用完整的图片URL - 修复：正确使用 fullurl
          const imageUrl = result.data.fullurl

          this.setData({
            'formData.device_images': imageUrl,
            selectedImage: imageUrl // 用于页面显示
          })
          wx.hideLoading()
          util.toast('图片上传成功')
        }).catch(err => {
          console.error('图片上传失败:', err)
          wx.hideLoading()

          // 提供更详细的错误信息
          let errorMsg = '图片上传失败，请重试'
          if (err && err.data && err.data.msg) {
            errorMsg = err.data.msg
          } else if (err && err.message) {
            errorMsg = err.message
          }

          util.toast(errorMsg)
        })
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        if (err.errMsg !== 'chooseImage:fail cancel') {
          util.toast('选择图片失败')
        }
      }
    })
  },

  /**
   * 删除图片
   */
  deleteImage() {
    wx.showModal({
      title: '提示',
      content: '确定要删除这张图片吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            'formData.device_images': '',
            selectedImage: ''
          })
          util.toast('删除成功')
        }
      }
    })
  },

  /**
   * 显示日期选择器
   */
  showDatePicker() {
    this.setData({
      showDatePopup: true
    })
  },

  /**
   * 隐藏日期选择器
   */
  hideDatePicker() {
    this.setData({
      showDatePopup: false
    })
  },

  /**
   * 日期选择变化
   */
  onDateChange(event) {
    this.setData({
      currentDate: event.detail
    })
  },

  /**
   * 确认选择日期
   */
  confirmDate(event) {
    const date = new Date(event.detail)
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    
    this.setData({
      'formData.purchaseDate': dateStr,
      showDatePopup: false
    })
  },

  /**
   * 检查表单是否可以提交
   */
  canSubmit() {
    const { device_name, device_sn, device_type, repair_period, device_images, name } = this.data.formData
    return device_name && device_sn && device_type && repair_period && device_images && name
  },

  /**
   * 提交表单
   */
  submitForm() {
    if (!this.canSubmit() || this.data.submitting) {
      return
    }

    // 表单验证
    if (!this.validateForm()) {
      return
    }

    this.setData({ submitting: true })

    console.log('提交设备数据：', this.data.formData)

    http.post('device/deviceAdd', this.data.formData).then(res => {
      console.log('添加设备成功：', res)
      util.toast('添加成功')
      // 缩短延时时间，并添加调试信息
      setTimeout(() => {
        console.log('准备返回上一页')
        wx.navigateBack({
          success: () => {
            console.log('返回上一页成功')
          },
          fail: (err) => {
            console.error('返回上一页失败:', err)
          }
        })
      }, 800) // 缩短到0.8秒
    }).catch(err => {
      console.error('添加设备失败:', err)
      util.toast(err.msg || '添加失败，请重试')
    }).finally(() => {
      this.setData({ submitting: false })
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { device_name, device_sn, device_type, repair_period, device_images, name } = this.data.formData
    
    if (!device_name.trim()) {
      util.toast('请输入设备名称')
      return false
    }
    
    if (!device_sn.trim()) {
      util.toast('请输入设备SN码')
      return false
    }
    
    if (!device_type) {
      util.toast('请选择设备类型')
      return false
    }
    
    if (!repair_period) {
      util.toast('请选择报修年限')
      return false
    }
    
    if (!device_images) {
      util.toast('请上传设备图片')
      return false
    }
    
    if (!name.trim()) {
      util.toast('请选择关联用户')
      return false
    }
    
    return true
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  }
})
