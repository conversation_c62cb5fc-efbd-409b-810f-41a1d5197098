/* 设备管理页面样式 */

/* 页面容器 */
.page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: 120rpx; /* 为底部固定按钮预留空间 */
  position: relative;
}

/* 导航栏容器样式 */
.van-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

/* 导航栏元素样式 */
.van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

.van-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

/* 头部区域 */
.header-section {
  display: flex;
  flex-direction: column;
  padding: 20rpx 32rpx 52rpx 32rpx;
  box-sizing: border-box;
  position: relative;
}

/* 搜索框容器 */
.search-wrapper {
  margin-top: 29rpx;
}

/* 搜索输入框样式 */
.search-input {
  background: #ffffff !important;
  border-radius: 36rpx !important;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.02) !important;
}

/* 搜索操作按钮 */
.search-action {
  color: #1782FA;
  font-size: 28rpx;
  padding: 0 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 内容区域 */
.content-section {
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;
  box-sizing: border-box;
  flex: 1;
}

/* 设备列表容器 */
.device-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 设备卡片项 */
.device-item {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
  display: flex;
  flex-direction: row;
  padding: 24rpx;
  box-sizing: border-box;
  position: relative;
}

/* 设备内容区域 */
.device-content {
  display: flex;
  flex-direction: row;
  flex: 1;
  gap: 20rpx;
}

/* 设备图片 */
.device-image {
  width: 176rpx;
  height: 176rpx;
  background-size: cover;
  background-position: center;
  border-radius: 8rpx;
  flex-shrink: 0;
}

/* 设备信息区域 */
.device-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
}

/* 设备名称 */
.device-name {
  color: rgba(51, 51, 51, 1);
  font-size: 28rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 40rpx;
  margin-bottom: 8rpx;
}

/* 设备SN码 */
.device-sn {
  color: rgba(153, 153, 153, 1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-bottom: 8rpx;
}

/* 设备用户信息 */
.device-user {
  color: rgba(153, 153, 153, 1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-bottom: 20rpx;
}

/* 维修记录信息容器 */
.record-info {
  display: flex;
  flex-direction: row;
  align-items: baseline;
  gap: 4rpx;
}

/* 维修记录数量 */
.record-count {
  color: rgba(23, 130, 250, 1);
  font-size: 40rpx;
  font-family: DINAlternate-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 48rpx;
}

/* 维修记录分隔符 */
.record-separator {
  color: rgba(138, 138, 138, 1);
  font-size: 24rpx;
  font-family: HelveticaNeue;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 11rpx;
}

/* 维修记录文字 */
.record-text {
  color: rgba(138, 138, 138, 1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 11rpx;
}

/* 添加记录按钮 */
.add-record-btn {
  background-color: rgba(23, 130, 250, 1);
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12rpx 20rpx;
  position: absolute;
  right: 24rpx;
  bottom: 24rpx; /* 改为底部对齐 */
  box-sizing: border-box;
}

/* 按钮文字 */
.btn-text {
  color: rgba(255, 255, 255, 1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 24rpx;
}

/* 底部固定按钮 */
.fixed-bottom-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 1);
  padding: 18rpx 32rpx;
  box-sizing: border-box;
  z-index: 999;
}

/* 底部按钮包装器 */
.btn-wrapper {
  background-color: rgba(23, 130, 250, 1);
  border-radius: 38rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16rpx 0;
  box-sizing: border-box;
}

/* 底部按钮文字 */
.fixed-bottom-btn .btn-text {
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 45rpx;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .device-image {
    width: 140rpx;
    height: 140rpx;
  }
  
  .device-name {
    font-size: 26rpx;
  }
  
  .device-sn,
  .device-user {
    font-size: 22rpx;
  }
  
  .record-count {
    font-size: 36rpx;
  }
}

/* 加载状态（预留） */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  color: rgba(153, 153, 153, 1);
  font-size: 28rpx;
}

/* 空状态（预留） */
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 80rpx 40rpx;
  color: rgba(153, 153, 153, 1);
  font-size: 28rpx;
}

.empty-state .empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}