import http from '../../../utils/http'
import util from '../../../utils/util'

Page({
  data: {
    searchValue: '', // 搜索关键词
    searchResults: [], // 搜索结果
    recommendUsers: [], // 推荐用户
    selectedUserId: null, // 已选择的用户ID（单选）
    loading: false // 加载状态
  },

  onLoad(options) {
    // 获取传入的参数
    if (options.selectedId) {
      this.setData({
        selectedUserId: parseInt(options.selectedId)
      })
    }

    // 加载推荐用户
    this.loadRecommendUsers()
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 搜索输入变化
   */
  onSearchChange(e) {
    const value = e.detail
    this.setData({
      searchValue: value
    })

    // 防抖搜索
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(() => {
      if (value.trim()) {
        this.searchUsers(value.trim())
      } else {
        this.setData({
          searchResults: []
        })
      }
    }, 300)
  },

  /**
   * 搜索确认
   */
  onSearch(e) {
    const value = e.detail
    if (value.trim()) {
      this.searchUsers(value.trim())
    }
  },

  /**
   * 搜索用户
   */
  searchUsers(keyword) {
    this.setData({
      loading: true
    })

    // 调用真实的用户列表接口
    http.get('user/userList', {
      q: keyword
    }).then(res => {
      console.log('搜索用户成功：', res)
      
      // 转换数据格式以适配页面显示
      const searchResults = res.data.map(user => ({
        id: user.id,
        name: user.nickname,
        phone: user.mobile,
        avatar: user.avatar
      }))
      
      this.setData({
        searchResults: searchResults,
        loading: false
      })
    }).catch(err => {
      console.error('搜索用户失败:', err)
      util.toast(err.msg || '搜索失败，请重试')
      this.setData({
        searchResults: [],
        loading: false
      })
    })
  },

  /**
   * 加载推荐用户
   */
  loadRecommendUsers() {
    this.setData({
      loading: true
    })

    // 调用真实的用户列表接口，不传搜索参数获取所有用户
    http.get('user/userList', {}).then(res => {
      console.log('加载用户列表成功：', res)
      
      // 转换数据格式以适配页面显示
      const recommendUsers = res.data.map(user => ({
        id: user.id,
        name: user.nickname,
        phone: user.mobile,
        avatar: user.avatar
      }))
      
      this.setData({
        recommendUsers: recommendUsers,
        loading: false
      })
    }).catch(err => {
      console.error('加载用户列表失败:', err)
      util.toast(err.msg || '加载失败，请重试')
      this.setData({
        recommendUsers: [],
        loading: false
      })
    })
  },

  /**
   * 选择用户（单选）
   */
  selectUser(e) {
    const user = e.currentTarget.dataset.user
    if (!user) {
      console.error('用户信息不存在')
      return
    }

    console.log('选择用户：', user)

    // 单选模式：直接设置选中的用户ID
    this.setData({
      selectedUserId: user.id
    })

    // 立即返回选中的用户
    this.returnSelectedUser(user)
  },

  /**
   * 返回选中的用户
   */
  returnSelectedUser(user) {
    // 通过EventChannel返回数据
    const eventChannel = this.getOpenerEventChannel()
    if (eventChannel) {
      eventChannel.emit('selectUser', user)
    }

    // 显示选择成功提示
    wx.showToast({
      title: '选择成功',
      icon: 'success',
      duration: 1000,
      success: () => {
        // 延迟返回，让用户看到选择反馈
        setTimeout(() => {
          wx.navigateBack()
        }, 1000)
      }
    })
  },

  onUnload() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  }
})