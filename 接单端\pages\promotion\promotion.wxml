<!-- 分销推广页面 -->
<view class="page">
  <!-- 头部用户信息区域 -->
  <view class="header-section">
    <!-- 导航栏 -->
    <van-nav-bar title="分销推广" left-arrow="{{true}}" bind:click-left="onClickLeft" custom-class="custom-nav-bar" />
    <!-- 用户头像和信息 -->
    <view class="user-info">
      <view class="avatar-wrapper">
        <view class="avatar-container">
          <view class="avatar-bg">
            <image wx:if="{{userInfo.avatar}}" src="{{userInfo.avatar}}" class="avatar-img" mode="aspectFill"></image>
            <view wx:else class="avatar-default"></view>
          </view>
        </view>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.name}}</text>
        <text class="commission-rate">提成比例：{{userInfo.commissionRate}}</text>
      </view>
   
    </view>
       <!-- 推广码按钮 -->
      <view class="promo-code-btn" bindtap="onPromoCodeTap">
        <view class="promo-code-content">
          <image src="/static/equipment/qrcode.png" class="promo-icon"></image>
          <text class="promo-text">推广码</text>
        </view>
      </view>
  </view>
  <!-- 收益卡片 -->
  <view class="earnings-card">
    <text class="card-title">可提现 (元)</text>
    <view class="amount-wrapper">
      <text class="currency-symbol">¥</text>
      <text class="amount">{{earnings.withdrawable}}</text>
    </view>
    <!-- 统计数据 -->
    <view class="stats-container">
      <view class="stat-item">
        <text class="stat-label">累计佣金 (元)</text>
        <text class="stat-value">{{earnings.totalCommission}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">已提现 (元)</text>
        <text class="stat-value">{{earnings.withdrawn}}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">总成交金额 (元)</text>
        <text class="stat-value">{{earnings.totalTransaction}}</text>
      </view>
    </view>
    <!-- 提现按钮 -->
    <view class="withdraw-btn" bindtap="onWithdrawTap">
      <text class="btn-text">我要提现</text>
    </view>
  </view>
  <!-- 常用工具区域 -->
  <view class="tools-section">
    <text class="section-title">常用工具</text>
    <view class="tools-card">
      <!-- 工具列表 -->
      <view class="tools-grid">
        <view class="tool-item" wx:for="{{toolsList}}" wx:key="id" bindtap="onToolTap" data-id="{{item.id}}" data-name="{{item.name}}">
          <image src="{{item.icon}}" class="tool-icon"></image>
          <view class="tool-info">
            <text class="tool-name">{{item.name}}</text>
            <text class="tool-desc">{{item.desc}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>