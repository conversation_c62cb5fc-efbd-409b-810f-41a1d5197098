// 分销推广页面
import http from '../../utils/http';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户信息
    userInfo: {
      name: '',
      commissionRate: '',
      avatar: ''
    },
    
    // 收益数据
    earnings: {
      withdrawable: '0.00',
      totalCommission: '0',
      withdrawn: '0',
      totalTransaction: '0'
    },
    
    // 工具列表
    toolsList: [
      {
        id: 1,
        name: '佣金明细',
        desc: '0元',
        icon: '/static/equipment/yjmx.png'
      },
      {
        id: 2,
        name: '提现记录',
        desc: '每一笔都有',
        icon: '/static/equipment/txjl.png'
      },
      {
        id: 3,
        name: '我的邀请',
        desc: '0个',
        icon: '/static/equipment/wdyq.png'
      }
    ],
    
    // 加载状态
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadDistributionInfo();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新数据
    this.loadDistributionInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadDistributionInfo().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '分销推广',
      path: '/pages/promotion/promotion'
    };
  },

  /**
   * 加载分销信息
   */
  loadDistributionInfo() {
    if (this.data.loading) return Promise.resolve();
    
    this.setData({ loading: true });
    
    return http.get('user/distributionInfo', {}, true, true)
      .then((res) => {
        console.log('分销信息接口响应:', res);
        
        const data = res.data;
        
        // 更新用户信息
        this.setData({
          'userInfo.name': data.nickname || '',
          'userInfo.commissionRate': data.distribution_ratio || '0%',
          'userInfo.avatar': data.avatar ? (data.avatar.startsWith('http') ? data.avatar : 'https://service.infooi.cn' + data.avatar) : ''
        });
        
        // 更新收益数据
        this.setData({
          'earnings.withdrawable': data.money || '0.00',
          'earnings.totalCommission': this.formatNumber(data.commission_money || 0),
          'earnings.withdrawn': this.formatNumber(data.withdraw || 0),
          'earnings.totalTransaction': this.formatNumber(data.money_nums || 0)
        });
        
        // 更新工具列表数据
        const updatedToolsList = [...this.data.toolsList];
        updatedToolsList[0].desc = this.formatNumber(data.commission_nums || 0) + '元';
        updatedToolsList[2].desc = (data.invite_nums || 0) + '个';
        
        this.setData({
          toolsList: updatedToolsList,
          loading: false
        });
        
        return res;
      })
      .catch((err) => {
        console.error('分销信息接口请求失败:', err);
        this.setData({ loading: false });
        throw err;
      });
  },

  /**
   * 格式化数字显示
   */
  formatNumber(num) {
    if (num === null || num === undefined) return '0';
    const number = Number(num);
    if (isNaN(number)) return '0';
    
    // 如果是整数，直接返回
    if (number % 1 === 0) {
      return number.toLocaleString();
    }
    
    // 如果是小数，保留两位小数
    return number.toFixed(2);
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  },

  /**
   * 点击推广码按钮
   */
  onPromoCodeTap() {
    wx.showToast({
      title: '推广码功能',
      icon: 'none'
    });
    // TODO: 实现推广码功能
  },

  /**
   * 点击提现按钮
   */
  onWithdrawTap() {
    wx.navigateTo({
      url: '/service/balance/balance'
    });
  },

  /**
   * 点击工具项
   */
  onToolTap(e) {
    const toolId = e.currentTarget.dataset.id;
    const toolName = e.currentTarget.dataset.name;
    
    switch(toolId) {
      case 1:
        // 佣金明细 - 跳转到分销订单页面
        wx.navigateTo({
          url: '/pages/distribution/distribution'
        });
        break;
      case 2:
        // 提现记录
        wx.navigateTo({
          url: '/service/withdraw/withdraw'
        });
        break;
      case 3:
        // 我的邀请
        wx.navigateTo({
          url: '/pages/distribution/distribution'
        });
        break;
      default:
        wx.showToast({
          title: toolName,
          icon: 'none'
        });
    }
  }
});