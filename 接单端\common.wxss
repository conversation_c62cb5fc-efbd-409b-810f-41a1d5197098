.z-margin-8 {
  margin: 8rpx !important;
}

.z-margin-t-8 {
  margin-top: 8rpx !important;
}

.z-margin-b-8 {
  margin-bottom: 8rpx !important;
}

.z-margin-l-8 {
  margin-left: 8rpx !important;
}

.z-margin-r-8 {
  margin-right: 8rpx !important;
}

.z-margin-tb-8 {
  margin-top: 8rpx !important;
  margin-bottom: 8rpx !important;
}

.z-margin-lr-8 {
  margin-left: 8rpx !important;
  margin-right: 8rpx !important;
}

.z-padding-8 {
  padding: 8rpx !important;
}

.z-padding-t-8 {
  padding-top: 8rpx !important;
}

.z-padding-b-8 {
  padding-bottom: 8rpx !important;
}

.z-padding-l-8 {
  padding-left: 8rpx !important;
}

.z-padding-r-8 {
  padding-right: 8rpx !important;
}

.z-padding-tb-8 {
  padding-top: 8rpx !important;
  padding-bottom: 8rpx !important;
}

.z-padding-lr-8 {
  padding-left: 8rpx !important;
  padding-right: 8rpx !important;
}

.z-margin-16 {
  margin: 16rpx !important;
}

.z-margin-t-16 {
  margin-top: 16rpx !important;
}

.z-margin-b-16 {
  margin-bottom: 16rpx !important;
}

.z-margin-l-16 {
  margin-left: 16rpx !important;
}

.z-margin-r-16 {
  margin-right: 16rpx !important;
}

.z-margin-tb-16 {
  margin-top: 16rpx !important;
  margin-bottom: 16rpx !important;
}

.z-margin-lr-16 {
  margin-left: 16rpx !important;
  margin-right: 16rpx !important;
}

.z-padding-16 {
  padding: 16rpx !important;
}

.z-padding-t-16 {
  padding-top: 16rpx !important;
}

.z-padding-b-16 {
  padding-bottom: 16rpx !important;
}

.z-padding-l-16 {
  padding-left: 16rpx !important;
}

.z-padding-r-16 {
  padding-right: 16rpx !important;
}

.z-padding-tb-16 {
  padding-top: 16rpx !important;
  padding-bottom: 16rpx !important;
}

.z-padding-lr-16 {
  padding-left: 16rpx !important;
  padding-right: 16rpx !important;
}

.z-margin-24 {
  margin: 24rpx !important;
}

.z-margin-t-24 {
  margin-top: 24rpx !important;
}

.z-margin-b-24 {
  margin-bottom: 24rpx !important;
}

.z-margin-l-24 {
  margin-left: 24rpx !important;
}

.z-margin-r-24 {
  margin-right: 24rpx !important;
}

.z-margin-tb-24 {
  margin-top: 24rpx !important;
  margin-bottom: 24rpx !important;
}

.z-margin-lr-24 {
  margin-left: 24rpx !important;
  margin-right: 24rpx !important;
}

.z-padding-24 {
  padding: 24rpx !important;
}

.z-padding-t-24 {
  padding-top: 24rpx !important;
}

.z-padding-b-24 {
  padding-bottom: 24rpx !important;
}

.z-padding-l-24 {
  padding-left: 24rpx !important;
}

.z-padding-r-24 {
  padding-right: 24rpx !important;
}

.z-padding-tb-24 {
  padding-top: 24rpx !important;
  padding-bottom: 24rpx !important;
}

.z-padding-lr-24 {
  padding-left: 24rpx !important;
  padding-right: 24rpx !important;
}

.z-margin-30 {
  margin: 30rpx !important;
}

.z-margin-t-30 {
  margin-top: 30rpx !important;
}

.z-margin-b-30 {
  margin-bottom: 30rpx !important;
}

.z-margin-l-30 {
  margin-left: 30rpx !important;
}

.z-margin-r-30 {
  margin-right: 30rpx !important;
}

.z-margin-tb-30 {
  margin-top: 30rpx !important;
  margin-bottom: 30rpx !important;
}

.z-margin-lr-30 {
  margin-left: 30rpx !important;
  margin-right: 30rpx !important;
}

.z-padding-30 {
  padding: 30rpx !important;
}

.z-padding-t-30 {
  padding-top: 30rpx !important;
}

.z-padding-b-30 {
  padding-bottom: 30rpx !important;
}

.z-padding-l-30 {
  padding-left: 30rpx !important;
}

.z-padding-r-30 {
  padding-right: 30rpx !important;
}

.z-padding-tb-30 {
  padding-top: 30rpx !important;
  padding-bottom: 30rpx !important;
}

.z-padding-lr-30 {
  padding-left: 30rpx !important;
  padding-right: 30rpx !important;
}

.z-margin-32 {
  margin: 32rpx !important;
}

.z-margin-t-32 {
  margin-top: 32rpx !important;
}

.z-margin-b-32 {
  margin-bottom: 32rpx !important;
}

.z-margin-l-32 {
  margin-left: 32rpx !important;
}

.z-margin-r-32 {
  margin-right: 32rpx !important;
}

.z-margin-tb-32 {
  margin-top: 32rpx !important;
  margin-bottom: 32rpx !important;
}

.z-margin-lr-32 {
  margin-left: 32rpx !important;
  margin-right: 32rpx !important;
}

.z-padding-32 {
  padding: 32rpx !important;
}

.z-padding-t-32 {
  padding-top: 32rpx !important;
}

.z-padding-b-32 {
  padding-bottom: 32rpx !important;
}

.z-padding-l-32 {
  padding-left: 32rpx !important;
}

.z-padding-r-32 {
  padding-right: 32rpx !important;
}

.z-padding-tb-32 {
  padding-top: 32rpx !important;
  padding-bottom: 32rpx !important;
}

.z-padding-lr-32 {
  padding-left: 32rpx !important;
  padding-right: 32rpx !important;
}

.z-margin-40 {
  margin: 40rpx !important;
}

.z-margin-t-40 {
  margin-top: 40rpx !important;
}

.z-margin-b-40 {
  margin-bottom: 40rpx !important;
}

.z-margin-l-40 {
  margin-left: 40rpx !important;
}

.z-margin-r-40 {
  margin-right: 40rpx !important;
}

.z-margin-tb-40 {
  margin-top: 40rpx !important;
  margin-bottom: 40rpx !important;
}

.z-margin-lr-40 {
  margin-left: 40rpx !important;
  margin-right: 40rpx !important;
}

.z-padding-40 {
  padding: 40rpx !important;
}

.z-padding-t-40 {
  padding-top: 40rpx !important;
}

.z-padding-b-40 {
  padding-bottom: 40rpx !important;
}

.z-padding-l-40 {
  padding-left: 40rpx !important;
}

.z-padding-r-40 {
  padding-right: 40rpx !important;
}

.z-padding-tb-40 {
  padding-top: 40rpx !important;
  padding-bottom: 40rpx !important;
}

.z-padding-lr-40 {
  padding-left: 40rpx !important;
  padding-right: 40rpx !important;
}

.z-margin-48 {
  margin: 48rpx !important;
}

.z-margin-t-48 {
  margin-top: 48rpx !important;
}

.z-margin-b-48 {
  margin-bottom: 48rpx !important;
}

.z-margin-l-48 {
  margin-left: 48rpx !important;
}

.z-margin-r-48 {
  margin-right: 48rpx !important;
}

.z-margin-tb-48 {
  margin-top: 48rpx !important;
  margin-bottom: 48rpx !important;
}

.z-margin-lr-48 {
  margin-left: 48rpx !important;
  margin-right: 48rpx !important;
}

.z-padding-48 {
  padding: 48rpx !important;
}

.z-padding-t-48 {
  padding-top: 48rpx !important;
}

.z-padding-b-48 {
  padding-bottom: 48rpx !important;
}

.z-padding-l-48 {
  padding-left: 48rpx !important;
}

.z-padding-r-48 {
  padding-right: 48rpx !important;
}

.z-padding-tb-48 {
  padding-top: 48rpx !important;
  padding-bottom: 48rpx !important;
}

.z-padding-lr-48 {
  padding-left: 48rpx !important;
  padding-right: 48rpx !important;
}

.z-margin-56 {
  margin: 56rpx !important;
}

.z-margin-t-56 {
  margin-top: 56rpx !important;
}

.z-margin-b-56 {
  margin-bottom: 56rpx !important;
}

.z-margin-l-56 {
  margin-left: 56rpx !important;
}

.z-margin-r-56 {
  margin-right: 56rpx !important;
}

.z-margin-tb-56 {
  margin-top: 56rpx !important;
  margin-bottom: 56rpx !important;
}

.z-margin-lr-56 {
  margin-left: 56rpx !important;
  margin-right: 56rpx !important;
}

.z-padding-56 {
  padding: 56rpx !important;
}

.z-padding-t-56 {
  padding-top: 56rpx !important;
}

.z-padding-b-56 {
  padding-bottom: 56rpx !important;
}

.z-padding-l-56 {
  padding-left: 56rpx !important;
}

.z-padding-r-56 {
  padding-right: 56rpx !important;
}

.z-padding-tb-56 {
  padding-top: 56rpx !important;
  padding-bottom: 56rpx !important;
}

.z-padding-lr-56 {
  padding-left: 56rpx !important;
  padding-right: 56rpx !important;
}

.z-margin-60 {
  margin: 60rpx !important;
}

.z-margin-t-60 {
  margin-top: 60rpx !important;
}

.z-margin-b-60 {
  margin-bottom: 60rpx !important;
}

.z-margin-l-60 {
  margin-left: 60rpx !important;
}

.z-margin-r-60 {
  margin-right: 60rpx !important;
}

.z-margin-tb-60 {
  margin-top: 60rpx !important;
  margin-bottom: 60rpx !important;
}

.z-margin-lr-60 {
  margin-left: 60rpx !important;
  margin-right: 60rpx !important;
}

.z-padding-60 {
  padding: 60rpx !important;
}

.z-padding-t-60 {
  padding-top: 60rpx !important;
}

.z-padding-b-60 {
  padding-bottom: 60rpx !important;
}

.z-padding-l-60 {
  padding-left: 60rpx !important;
}

.z-padding-r-60 {
  padding-right: 60rpx !important;
}

.z-padding-tb-60 {
  padding-top: 60rpx !important;
  padding-bottom: 60rpx !important;
}

.z-padding-lr-60 {
  padding-left: 60rpx !important;
  padding-right: 60rpx !important;
}

.z-margin-64 {
  margin: 64rpx !important;
}

.z-margin-t-64 {
  margin-top: 64rpx !important;
}

.z-margin-b-64 {
  margin-bottom: 64rpx !important;
}

.z-margin-l-64 {
  margin-left: 64rpx !important;
}

.z-margin-r-64 {
  margin-right: 64rpx !important;
}

.z-margin-tb-64 {
  margin-top: 64rpx !important;
  margin-bottom: 64rpx !important;
}

.z-margin-lr-64 {
  margin-left: 64rpx !important;
  margin-right: 64rpx !important;
}

.z-padding-64 {
  padding: 64rpx !important;
}

.z-padding-t-64 {
  padding-top: 64rpx !important;
}

.z-padding-b-64 {
  padding-bottom: 64rpx !important;
}

.z-padding-l-64 {
  padding-left: 64rpx !important;
}

.z-padding-r-64 {
  padding-right: 64rpx !important;
}

.z-padding-tb-64 {
  padding-top: 64rpx !important;
  padding-bottom: 64rpx !important;
}

.z-padding-lr-64 {
  padding-left: 64rpx !important;
  padding-right: 64rpx !important;
}

.z-margin-72 {
  margin: 72rpx !important;
}

.z-margin-t-72 {
  margin-top: 72rpx !important;
}

.z-margin-b-72 {
  margin-bottom: 72rpx !important;
}

.z-margin-l-72 {
  margin-left: 72rpx !important;
}

.z-margin-r-72 {
  margin-right: 72rpx !important;
}

.z-margin-tb-72 {
  margin-top: 72rpx !important;
  margin-bottom: 72rpx !important;
}

.z-margin-lr-72 {
  margin-left: 72rpx !important;
  margin-right: 72rpx !important;
}

.z-padding-72 {
  padding: 72rpx !important;
}

.z-padding-t-72 {
  padding-top: 72rpx !important;
}

.z-padding-b-72 {
  padding-bottom: 72rpx !important;
}

.z-padding-l-72 {
  padding-left: 72rpx !important;
}

.z-padding-r-72 {
  padding-right: 72rpx !important;
}

.z-padding-tb-72 {
  padding-top: 72rpx !important;
  padding-bottom: 72rpx !important;
}

.z-padding-lr-72 {
  padding-left: 72rpx !important;
  padding-right: 72rpx !important;
}

.z-margin-80 {
  margin: 80rpx !important;
}

.z-margin-t-80 {
  margin-top: 80rpx !important;
}

.z-margin-b-80 {
  margin-bottom: 80rpx !important;
}

.z-margin-l-80 {
  margin-left: 80rpx !important;
}

.z-margin-r-80 {
  margin-right: 80rpx !important;
}

.z-margin-tb-80 {
  margin-top: 80rpx !important;
  margin-bottom: 80rpx !important;
}

.z-margin-lr-80 {
  margin-left: 80rpx !important;
  margin-right: 80rpx !important;
}

.z-padding-80 {
  padding: 80rpx !important;
}

.z-padding-t-80 {
  padding-top: 80rpx !important;
}

.z-padding-b-80 {
  padding-bottom: 80rpx !important;
}

.z-padding-l-80 {
  padding-left: 80rpx !important;
}

.z-padding-r-80 {
  padding-right: 80rpx !important;
}

.z-padding-tb-80 {
  padding-top: 80rpx !important;
  padding-bottom: 80rpx !important;
}

.z-padding-lr-80 {
  padding-left: 80rpx !important;
  padding-right: 80rpx !important;
}

.z-margin-88 {
  margin: 88rpx !important;
}

.z-margin-t-88 {
  margin-top: 88rpx !important;
}

.z-margin-b-88 {
  margin-bottom: 88rpx !important;
}

.z-margin-l-88 {
  margin-left: 88rpx !important;
}

.z-margin-r-88 {
  margin-right: 88rpx !important;
}

.z-margin-tb-88 {
  margin-top: 88rpx !important;
  margin-bottom: 88rpx !important;
}

.z-margin-lr-88 {
  margin-left: 88rpx !important;
  margin-right: 88rpx !important;
}

.z-padding-88 {
  padding: 88rpx !important;
}

.z-padding-t-88 {
  padding-top: 88rpx !important;
}

.z-padding-b-88 {
  padding-bottom: 88rpx !important;
}

.z-padding-l-88 {
  padding-left: 88rpx !important;
}

.z-padding-r-88 {
  padding-right: 88rpx !important;
}

.z-padding-tb-88 {
  padding-top: 88rpx !important;
  padding-bottom: 88rpx !important;
}

.z-padding-lr-88 {
  padding-left: 88rpx !important;
  padding-right: 88rpx !important;
}

.z-margin-90 {
  margin: 90rpx !important;
}

.z-margin-t-90 {
  margin-top: 90rpx !important;
}

.z-margin-b-90 {
  margin-bottom: 90rpx !important;
}

.z-margin-l-90 {
  margin-left: 90rpx !important;
}

.z-margin-r-90 {
  margin-right: 90rpx !important;
}

.z-margin-tb-90 {
  margin-top: 90rpx !important;
  margin-bottom: 90rpx !important;
}

.z-margin-lr-90 {
  margin-left: 90rpx !important;
  margin-right: 90rpx !important;
}

.z-padding-90 {
  padding: 90rpx !important;
}

.z-padding-t-90 {
  padding-top: 90rpx !important;
}

.z-padding-b-90 {
  padding-bottom: 90rpx !important;
}

.z-padding-l-90 {
  padding-left: 90rpx !important;
}

.z-padding-r-90 {
  padding-right: 90rpx !important;
}

.z-padding-tb-90 {
  padding-top: 90rpx !important;
  padding-bottom: 90rpx !important;
}

.z-padding-lr-90 {
  padding-left: 90rpx !important;
  padding-right: 90rpx !important;
}

.z-margin-96 {
  margin: 96rpx !important;
}

.z-margin-t-96 {
  margin-top: 96rpx !important;
}

.z-margin-b-96 {
  margin-bottom: 96rpx !important;
}

.z-margin-l-96 {
  margin-left: 96rpx !important;
}

.z-margin-r-96 {
  margin-right: 96rpx !important;
}

.z-margin-tb-96 {
  margin-top: 96rpx !important;
  margin-bottom: 96rpx !important;
}

.z-margin-lr-96 {
  margin-left: 96rpx !important;
  margin-right: 96rpx !important;
}

.z-padding-96 {
  padding: 96rpx !important;
}

.z-padding-t-96 {
  padding-top: 96rpx !important;
}

.z-padding-b-96 {
  padding-bottom: 96rpx !important;
}

.z-padding-l-96 {
  padding-left: 96rpx !important;
}

.z-padding-r-96 {
  padding-right: 96rpx !important;
}

.z-padding-tb-96 {
  padding-top: 96rpx !important;
  padding-bottom: 96rpx !important;
}

.z-padding-lr-96 {
  padding-left: 96rpx !important;
  padding-right: 96rpx !important;
}

.z-margin-100 {
  margin: 100rpx !important;
}

.z-margin-t-100 {
  margin-top: 100rpx !important;
}

.z-margin-b-100 {
  margin-bottom: 100rpx !important;
}

.z-margin-l-100 {
  margin-left: 100rpx !important;
}

.z-margin-r-100 {
  margin-right: 100rpx !important;
}

.z-margin-tb-100 {
  margin-top: 100rpx !important;
  margin-bottom: 100rpx !important;
}

.z-margin-lr-100 {
  margin-left: 100rpx !important;
  margin-right: 100rpx !important;
}

.z-padding-100 {
  padding: 100rpx !important;
}

.z-padding-t-100 {
  padding-top: 100rpx !important;
}

.z-padding-b-100 {
  padding-bottom: 100rpx !important;
}

.z-padding-l-100 {
  padding-left: 100rpx !important;
}

.z-padding-r-100 {
  padding-right: 100rpx !important;
}

.z-padding-tb-100 {
  padding-top: 100rpx !important;
  padding-bottom: 100rpx !important;
}

.z-padding-lr-100 {
  padding-left: 100rpx !important;
  padding-right: 100rpx !important;
}

.z-radius-4 {
  border-radius: 4rpx !important;
}

.z-radius-tl-4 {
  border-top-left-radius: 4rpx !important;
}

.z-radius-tr-4 {
  border-top-right-radius: 4rpx !important;
}

.z-radius-bl-4 {
  border-bottom-left-radius: 4rpx !important;
}

.z-radius-br-4 {
  border-bottom-right-radius: 4rpx !important;
}

.z-radius-t-4 {
  border-top-left-radius: 4rpx !important;
  border-top-right-radius: 4rpx !important;
}

.z-radius-b-4 {
  border-bottom-left-radius: 4rpx !important;
  border-bottom-right-radius: 4rpx !important;
}

.z-radius-8 {
  border-radius: 8rpx !important;
}

.z-radius-tl-8 {
  border-top-left-radius: 8rpx !important;
}

.z-radius-tr-8 {
  border-top-right-radius: 8rpx !important;
}

.z-radius-bl-8 {
  border-bottom-left-radius: 8rpx !important;
}

.z-radius-br-8 {
  border-bottom-right-radius: 8rpx !important;
}

.z-radius-t-8 {
  border-top-left-radius: 8rpx !important;
  border-top-right-radius: 8rpx !important;
}

.z-radius-b-8 {
  border-bottom-left-radius: 8rpx !important;
  border-bottom-right-radius: 8rpx !important;
}

.z-radius-12 {
  border-radius: 12rpx !important;
}

.z-radius-tl-12 {
  border-top-left-radius: 12rpx !important;
}

.z-radius-tr-12 {
  border-top-right-radius: 12rpx !important;
}

.z-radius-bl-12 {
  border-bottom-left-radius: 12rpx !important;
}

.z-radius-br-12 {
  border-bottom-right-radius: 12rpx !important;
}

.z-radius-t-12 {
  border-top-left-radius: 12rpx !important;
  border-top-right-radius: 12rpx !important;
}

.z-radius-b-12 {
  border-bottom-left-radius: 12rpx !important;
  border-bottom-right-radius: 12rpx !important;
}

.z-radius-16 {
  border-radius: 16rpx !important;
}

.z-radius-tl-16 {
  border-top-left-radius: 16rpx !important;
}

.z-radius-tr-16 {
  border-top-right-radius: 16rpx !important;
}

.z-radius-bl-16 {
  border-bottom-left-radius: 16rpx !important;
}

.z-radius-br-16 {
  border-bottom-right-radius: 16rpx !important;
}

.z-radius-t-16 {
  border-top-left-radius: 16rpx !important;
  border-top-right-radius: 16rpx !important;
}

.z-radius-b-16 {
  border-bottom-left-radius: 16rpx !important;
  border-bottom-right-radius: 16rpx !important;
}

.z-radius-20 {
  border-radius: 20rpx !important;
}

.z-radius-tl-20 {
  border-top-left-radius: 20rpx !important;
}

.z-radius-tr-20 {
  border-top-right-radius: 20rpx !important;
}

.z-radius-bl-20 {
  border-bottom-left-radius: 20rpx !important;
}

.z-radius-br-20 {
  border-bottom-right-radius: 20rpx !important;
}

.z-radius-t-20 {
  border-top-left-radius: 20rpx !important;
  border-top-right-radius: 20rpx !important;
}

.z-radius-b-20 {
  border-bottom-left-radius: 20rpx !important;
  border-bottom-right-radius: 20rpx !important;
}

.z-radius-24 {
  border-radius: 24rpx !important;
}

.z-radius-tl-24 {
  border-top-left-radius: 24rpx !important;
}

.z-radius-tr-24 {
  border-top-right-radius: 24rpx !important;
}

.z-radius-bl-24 {
  border-bottom-left-radius: 24rpx !important;
}

.z-radius-br-24 {
  border-bottom-right-radius: 24rpx !important;
}

.z-radius-t-24 {
  border-top-left-radius: 24rpx !important;
  border-top-right-radius: 24rpx !important;
}

.z-radius-b-24 {
  border-bottom-left-radius: 24rpx !important;
  border-bottom-right-radius: 24rpx !important;
}

.z-radius-28 {
  border-radius: 28rpx !important;
}

.z-radius-tl-28 {
  border-top-left-radius: 28rpx !important;
}

.z-radius-tr-28 {
  border-top-right-radius: 28rpx !important;
}

.z-radius-bl-28 {
  border-bottom-left-radius: 28rpx !important;
}

.z-radius-br-28 {
  border-bottom-right-radius: 28rpx !important;
}

.z-radius-t-28 {
  border-top-left-radius: 28rpx !important;
  border-top-right-radius: 28rpx !important;
}

.z-radius-b-28 {
  border-bottom-left-radius: 28rpx !important;
  border-bottom-right-radius: 28rpx !important;
}

.z-radius-30 {
  border-radius: 30rpx !important;
}

.z-radius-tl-30 {
  border-top-left-radius: 30rpx !important;
}

.z-radius-tr-30 {
  border-top-right-radius: 30rpx !important;
}

.z-radius-bl-30 {
  border-bottom-left-radius: 30rpx !important;
}

.z-radius-br-30 {
  border-bottom-right-radius: 30rpx !important;
}

.z-radius-t-30 {
  border-top-left-radius: 30rpx !important;
  border-top-right-radius: 30rpx !important;
}

.z-radius-b-30 {
  border-bottom-left-radius: 30rpx !important;
  border-bottom-right-radius: 30rpx !important;
}

.z-radius-32 {
  border-radius: 32rpx !important;
}

.z-radius-tl-32 {
  border-top-left-radius: 32rpx !important;
}

.z-radius-tr-32 {
  border-top-right-radius: 32rpx !important;
}

.z-radius-bl-32 {
  border-bottom-left-radius: 32rpx !important;
}

.z-radius-br-32 {
  border-bottom-right-radius: 32rpx !important;
}

.z-radius-t-32 {
  border-top-left-radius: 32rpx !important;
  border-top-right-radius: 32rpx !important;
}

.z-radius-b-32 {
  border-bottom-left-radius: 32rpx !important;
  border-bottom-right-radius: 32rpx !important;
}

.z-radius-36 {
  border-radius: 36rpx !important;
}

.z-radius-tl-36 {
  border-top-left-radius: 36rpx !important;
}

.z-radius-tr-36 {
  border-top-right-radius: 36rpx !important;
}

.z-radius-bl-36 {
  border-bottom-left-radius: 36rpx !important;
}

.z-radius-br-36 {
  border-bottom-right-radius: 36rpx !important;
}

.z-radius-t-36 {
  border-top-left-radius: 36rpx !important;
  border-top-right-radius: 36rpx !important;
}

.z-radius-b-36 {
  border-bottom-left-radius: 36rpx !important;
  border-bottom-right-radius: 36rpx !important;
}

.z-radius-40 {
  border-radius: 40rpx !important;
}

.z-radius-tl-40 {
  border-top-left-radius: 40rpx !important;
}

.z-radius-tr-40 {
  border-top-right-radius: 40rpx !important;
}

.z-radius-bl-40 {
  border-bottom-left-radius: 40rpx !important;
}

.z-radius-br-40 {
  border-bottom-right-radius: 40rpx !important;
}

.z-radius-t-40 {
  border-top-left-radius: 40rpx !important;
  border-top-right-radius: 40rpx !important;
}

.z-radius-b-40 {
  border-bottom-left-radius: 40rpx !important;
  border-bottom-right-radius: 40rpx !important;
}

.z-radius-44 {
  border-radius: 44rpx !important;
}

.z-radius-tl-44 {
  border-top-left-radius: 44rpx !important;
}

.z-radius-tr-44 {
  border-top-right-radius: 44rpx !important;
}

.z-radius-bl-44 {
  border-bottom-left-radius: 44rpx !important;
}

.z-radius-br-44 {
  border-bottom-right-radius: 44rpx !important;
}

.z-radius-t-44 {
  border-top-left-radius: 44rpx !important;
  border-top-right-radius: 44rpx !important;
}

.z-radius-b-44 {
  border-bottom-left-radius: 44rpx !important;
  border-bottom-right-radius: 44rpx !important;
}

.z-radius-48 {
  border-radius: 48rpx !important;
}

.z-radius-tl-48 {
  border-top-left-radius: 48rpx !important;
}

.z-radius-tr-48 {
  border-top-right-radius: 48rpx !important;
}

.z-radius-bl-48 {
  border-bottom-left-radius: 48rpx !important;
}

.z-radius-br-48 {
  border-bottom-right-radius: 48rpx !important;
}

.z-radius-t-48 {
  border-top-left-radius: 48rpx !important;
  border-top-right-radius: 48rpx !important;
}

.z-radius-b-48 {
  border-bottom-left-radius: 48rpx !important;
  border-bottom-right-radius: 48rpx !important;
}

.z-radius-50 {
  border-radius: 50rpx !important;
}

.z-radius-tl-50 {
  border-top-left-radius: 50rpx !important;
}

.z-radius-tr-50 {
  border-top-right-radius: 50rpx !important;
}

.z-radius-bl-50 {
  border-bottom-left-radius: 50rpx !important;
}

.z-radius-br-50 {
  border-bottom-right-radius: 50rpx !important;
}

.z-radius-t-50 {
  border-top-left-radius: 50rpx !important;
  border-top-right-radius: 50rpx !important;
}

.z-radius-b-50 {
  border-bottom-left-radius: 50rpx !important;
  border-bottom-right-radius: 50rpx !important;
}

.z-radius{
  border-radius: 50%;
}

.z-font-18 {
  font-size: 18rpx !important;
}

.z-font-20 {
  font-size: 20rpx !important;
}

.z-font-22 {
  font-size: 22rpx !important;
}

.z-font-24 {
  font-size: 24rpx !important;
}

.z-font-26 {
  font-size: 26rpx !important;
}

.z-font-28 {
  font-size: 28rpx !important;
}

.z-font-30 {
  font-size: 30rpx !important;
}

.z-font-32 {
  font-size: 32rpx !important;
}

.z-font-36 {
  font-size: 36rpx !important;
}

.z-font-40 {
  font-size: 40rpx !important;
}

.z-font-56 {
  font-size: 56rpx !important;
}

.z-font-64 {
  font-size: 64rpx !important;
}

.z-font-w {
  font-weight: bold;
}

.hidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}


.z-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}


.z-flex-x-c {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.z-flex-y-c {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.z-flex-c-s-b {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.z-flex-c{
  display: flex;
  align-items: center;
}

.z-flex-1{
	flex: 1;
}

.z-flex-0{
  flex-shrink: 0;
}

.z-text-l {
  text-align: left;
}

.z-text-r {
  text-align: right;
}

.z-text-c {
  text-align: center;
}

.z-border-b {
border-bottom: 1rpx solid rgba(164,169,183,0.2);
}
.theme-colors {
  background-color: #1677FF;
}
.theme-color {
  color: #1677FF;
}

.text_666 {
  color: #666;
}

.text_333 {
  color: #333;
}

.text_999 {
  color: #A4A9B7;
}

.z-btn{
  background: #1782FA;
  color: #fff;
  border-radius: 44rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}


.z-absolute{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

/* 通用页面背景色样式 */
.page-bg {
  width: 750rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, #C5E0FF 0%, #F6F7F8 582rpx, #F6F7F8 100%);
}

/* 通用导航栏样式 */
.common-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

.common-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

.common-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

/* 通用页面容器 */
.common-page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}


.van-hairline--bottom:after {
    border-bottom-width: 0px!important;
}