/* 设备详情页面样式 */

/* Tab切换容器 */


.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #666666;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #1782FA;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: #1782FA;
  border-radius: 12rpx;
  transition: transform 0.3s ease;
  opacity: 0.1;
  z-index: 1;
}

/* 内容容器 */
.content-container {
  padding: 0 24rpx;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  background: #fff;
  padding: 24rpx;
  border-radius: 12rpx;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: calc(50% - 8rpx);
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.device-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 28rpx;
  color: #1782FA;
  text-decoration: underline;
}

/* 维修记录时间轴 */
.records-timeline {
  min-height: 400rpx;
  padding: 0 24rpx;
}

.record-timeline-item {
  display: flex;
  position: relative;
  margin-bottom: 32rpx;
}

.record-timeline-item:last-child {
  margin-bottom: 0;
}

/* 时间轴节点 */
.timeline-node {
  position: relative;
  width: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.timeline-dot {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  z-index: 2;
  margin-top: 35rpx;

  background: #2C6BFE;
  opacity: 0.2;
}

.timeline-line {
  width: 2rpx;
  height: 100%;
  background: #e8e8e8;
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  min-height: 200rpx;
}

/* 记录卡片 */
.record-card {
  flex: 1;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.record-card::before {
  content: '';
  position: absolute;
  left: -8rpx;
  top: 35rpx;
  width: 0;
  height: 0;
  border-top: 8rpx solid transparent;
  border-bottom: 8rpx solid transparent;
  border-right: 8rpx solid #ffffff;
}

/* 记录时间 */
.record-time {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

/* 维修师傅信息 */
.record-technician {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.technician-label {
  font-size: 28rpx;
  color: #999999;
  margin-right: 16rpx;
}

.technician-name {
  font-size: 28rpx;
  color: #333333;
}

/* 维修记录描述 */
.record-description {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.description-label {
  font-size: 28rpx;
  color: #999999;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.description-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  flex: 1;
}

/* 维修图片 */
.record-images {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.record-image-item {
  width: 142rpx;
  height: 142rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f5f5f5;
}

.record-image {
  width: 100%;
  height: 100%;
}
/* 设备详情页面样式 */



.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #666666;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #1782FA;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: #1782FA;
  border-radius: 12rpx;
  transition: transform 0.3s ease;
  opacity: 0.1;
  z-index: 1;
}

/* 内容容器 */
.content-container {
  padding: 0 24rpx;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: calc(50% - 8rpx);
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.device-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 28rpx;
  color: #1782FA;
  text-decoration: underline;
}

/* 设备详情页面样式 */

/* Tab切换容器 */
.tab-container {
  position: relative;
  display: flex;
  background: #FFF!important;
  padding: 8rpx;
  box-shadow: none!important;
  margin-bottom: 24rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #666666;
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.tab-item.active {
  color: #1782FA;
  font-weight: 500;
}

.tab-indicator {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: calc(50% - 8rpx);
  height: calc(100% - 16rpx);
  background: #1782FA;
  border-radius: 12rpx;
  transition: transform 0.3s ease;
  opacity: 0.1;
  z-index: 1;
}

/* 内容容器 */
.content-container {
  padding: 0 24rpx;
  padding-bottom: 120rpx; /* 为底部按钮预留空间 */
}

/* 信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #666666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333333;
  flex: 1;
  text-align: right;
  word-break: break-all;
}

/* 备注内容 */
.remark-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  background: #fff;
  padding: 24rpx;
  border-radius: 12rpx;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  width: calc(50% - 8rpx);
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.device-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 用户信息 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 24rpx;
  background: #f5f5f5;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.user-phone {
  font-size: 28rpx;
  color: #1782FA;
  text-decoration: underline;
}

/* 维修记录 */
.records-container {
  min-height: 400rpx;
}

.record-item {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #1782FA;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.record-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.record-date {
  font-size: 24rpx;
  color: #999999;
}

.record-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.record-status {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-completed {
  background: #e8f5e8;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-processing {
  background: #e6f7ff;
  color: #1890ff;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999999;
}

/* 底部按钮 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.add-record-button.van-button.van-button--primary {
  width: 100% !important;
  height: 88rpx !important;
  background: #1782FA !important;
  border: none !important;
  border-radius: 44rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  color: #ffffff !important;
}

.add-record-button.van-button.van-button--primary:active {
  background: #1565c0 !important;
}

/* 导航栏样式 */
.van-nav-bar {
  background: #FFF !important;
  border-bottom: none !important;
}

.van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #333333 !important;
}

.van-nav-bar__title {
  color: #333333 !important;
  font-weight: 500 !important;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .info-card {
    padding: 24rpx;
  }
  
  .card-title {
    font-size: 30rpx;
  }
  
  .info-label, .info-value {
    font-size: 28rpx;
  }
}