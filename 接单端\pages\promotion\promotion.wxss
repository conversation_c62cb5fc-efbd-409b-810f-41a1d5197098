/* 引入通用样式 */
@import "/common.wxss";

/* 自定义导航栏样式 */
.custom-nav-bar {
  background: transparent !important;
  border-bottom: none !important;
}

/* 导航栏标题样式 */
.custom-nav-bar .van-nav-bar__title {
  color: #ffffff !important;
  font-weight: 500 !important;
}

/* 导航栏左侧返回按钮样式 */
.custom-nav-bar .van-nav-bar__left .van-icon {
  font-size: 44rpx !important;
  color: #ffffff !important;
}

/* 分销推广页面样式 */
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F5F6FA;
}

/* 头部区域 */
.header-section {
  background: url(https://service.infooi.cn/uploads/20250803/b25ea60a0f2d6ae49d666d8f5bf25ab9.png) no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  padding: 0rpx 24rpx 263rpx 24rpx;
  position: relative;
}

/* 用户信息区域 */
.user-info {
  display: flex;
  flex-direction: row;
  margin-top: 50rpx;
  padding-left: 16rpx;
}

/* 头像容器 */
.avatar-wrapper {
  display: flex;
  align-items: center;
}

.avatar-container {
  border-radius: 50%;
}

.avatar-bg {
  background-color: #ecf2fa;
  border-radius: 50%;
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.avatar-default {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/ca997b3c580749f2bf8305b322abdf09_mergeImage.png);
  width: 100rpx;
  height: 100rpx;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
}

/* 用户详情 */
.user-details {
  display: flex;
  flex-direction: column;
  margin-left: 24rpx;
  justify-content: center;
}

.user-name {
  color: #ffffff;
  font-size: 40rpx;
  font-weight: 600;
  line-height: 56rpx;
}

.commission-rate {

  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #FFFFFF;
  line-height: 26rpx;
  text-align: left;
  font-style: normal;
  opacity: 0.8;
  margin-top: 10rpx;
}

/* 推广码按钮 */
.promo-code-btn {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 15rpx 28rpx;
    align-self: flex-end;
    margin-top: 22rpx;
    position: fixed;
    top: 235rpx;
    right: 0px;
    border-bottom-left-radius: 50rpx;
    border-top-left-radius: 50rpx;
}

.promo-code-content {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.promo-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.promo-text {
  color: #ffffff;
  font-size: 26rpx;
  line-height: 26rpx;
}

/* 收益卡片 */
.earnings-card {
    background-color: #ffffff;
    background-image: url(https://service.infooi.cn/uploads/20250804/591eee9….png);
    background-repeat: no-repeat;
    background-position: right top;
    background-size: 414rpx 187rpx;
    border-radius: 16rpx;
    margin-top: -210rpx!important;
    margin: 24rpx;
    padding: 40rpx 32rpx 32rpx 32rpx;
    box-sizing: border-box;
    overflow: hidden;
    z-index: 1;
}

.card-title {
  color: #666666;
  font-size: 26rpx;
  line-height: 26rpx;
}

.amount-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-top: 24rpx;
}

.currency-symbol {
  color: #343434;
  font-size: 28rpx;
  font-weight: 700;
  line-height: 28rpx;
  margin-right: 8rpx;
  margin-bottom: 9rpx;
}

.amount {
  color: #343434;
  font-size: 46rpx;
  font-weight: 700;
  line-height: 46rpx;
}

.divider {
  background-color: #ededed;
  height: 1rpx;
  margin-top: 40rpx;
}

/* 统计数据容器 */
.stats-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 60rpx;
}

/* 统计数据项 */
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

}

.stat-item:nth-child(2) {
  align-items: flex-start;
}

.stat-item:nth-child(3) {
  align-items: flex-start;
}

.stat-item:nth-child(1) {
  align-items: flex-start;
}

/* 统计标签样式 */
.stat-label {
  color: #666666;
  font-size: 24rpx;
  line-height: 24rpx;
  margin-bottom: 20rpx;
}

/* 统计数值样式 */
.stat-value {
  color: #343434;
  font-size: 32rpx;
  line-height: 32rpx;
  font-weight: 500;
}

/* 提现按钮 */
.withdraw-btn {
  background-color: #1782fa;
  border-radius: 44rpx;
  margin-top: 40rpx;
  padding: 24rpx;
  text-align: center;
}

.btn-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 40rpx;
}

/* 装饰图片 */
.decoration-img {
  position: absolute;
  right: 32rpx;
  top: 3rpx;
  width: 414rpx;
  height: 187rpx;
  z-index: 0;
}

/* 常用工具区域 */
.tools-section {
  padding: 24rpx;
}

.section-title {
  color: #343434;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 45rpx;
}

.tools-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-top: 24rpx;
  padding: 40rpx 35rpx 53rpx 35rpx;
}

/* 工具网格 */
.tools-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
}

/* 工具项 */
.tool-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 48%;
  padding: 20rpx 0;
  box-sizing: border-box;
}

/* 第一列工具项（奇数项）*/
.tool-item:nth-child(odd) {
  margin-right: 4%;
}

/* 第二列工具项（偶数项）*/
.tool-item:nth-child(even) {
  margin-left: 0;
  justify-content: center;
}

/* 第二行及以后的工具项添加上边距 */
.tool-item:nth-child(n+3) {
  margin-top: 40rpx;
}

.tool-icon {
  width: 52rpx;
  height: 52rpx;
}

.tool-info {
  display: flex;
  flex-direction: column;
  margin-left: 23rpx;
}

.tool-name {
  color: #333333;
  font-size: 26rpx;
  font-weight: 600;
  line-height: 26rpx;
}

.tool-desc {
  color: #999999;
  font-size: 24rpx;
  line-height: 24rpx;
  margin-top: 12rpx;
}

/* 底部装饰 */
.bottom-decoration {
  width: 100%;
  height: 68rpx;
  margin-top: -1rpx;
}