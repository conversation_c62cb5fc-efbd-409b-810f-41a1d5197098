// 分销订单页面
import http from '../../utils/http';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 订单列表
    orderList: [
      {
        id: 1,
        userName: '李钰',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 2,
        userName: '陈浩雨',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 3,
        userName: '王怡宜',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 4,
        userName: '徐秀华',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 5,
        userName: '苏亦梅',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      },
      {
        id: 6,
        userName: '叶小海',
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: '89.23',
        commission: '88.88'
      }
    ],
    
    // 加载状态
    loading: false,
    hasMore: true,
    page: 1
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadOrderList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新数据
    this.refreshData();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '分销订单',
      path: '/pages/distribution/distribution'
    };
  },

  /**
   * 导航栏左侧返回按钮点击事件
   */
  onClickLeft() {
    wx.navigateBack();
  },

  /**
   * 刷新数据
   */
  refreshData() {
    this.setData({
      page: 1,
      hasMore: true,
      orderList: []
    });
    return this.loadOrderList();
  },

  /**
   * 加载更多数据
   */
  loadMoreData() {
    const nextPage = this.data.page + 1;
    this.setData({ page: nextPage });
    return this.loadOrderList(false);
  },

  /**
   * 加载订单列表
   */
  loadOrderList(showLoading = true) {
    if (this.data.loading) return Promise.resolve();
    
    this.setData({ loading: true });
    
    // 模拟接口调用
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = this.generateMockData();
        
        if (this.data.page === 1) {
          this.setData({
            orderList: mockData,
            loading: false
          });
        } else {
          this.setData({
            orderList: [...this.data.orderList, ...mockData],
            loading: false,
            hasMore: mockData.length > 0
          });
        }
        
        resolve();
      }, 1000);
    });
    
    // 实际接口调用代码（注释掉的示例）
    /*
    return http.get('distribution/orderList', {
      page: this.data.page,
      limit: 10
    }, showLoading, true)
      .then((res) => {
        const newData = res.data.list || [];
        
        if (this.data.page === 1) {
          this.setData({
            orderList: newData,
            loading: false
          });
        } else {
          this.setData({
            orderList: [...this.data.orderList, ...newData],
            loading: false,
            hasMore: newData.length > 0
          });
        }
        
        return res;
      })
      .catch((err) => {
        console.error('加载订单列表失败:', err);
        this.setData({ loading: false });
        throw err;
      });
    */
  },

  /**
   * 生成模拟数据
   */
  generateMockData() {
    const names = ['李钰', '陈浩雨', '王怡宜', '徐秀华', '苏亦梅', '叶小海', '张三', '李四', '王五', '赵六'];
    const mockData = [];
    
    for (let i = 0; i < 6; i++) {
      mockData.push({
        id: Date.now() + i,
        userName: names[Math.floor(Math.random() * names.length)],
        avatar: '',
        orderTime: '2025.04.16 19:23',
        orderAmount: (Math.random() * 200 + 50).toFixed(2),
        commission: (Math.random() * 100 + 10).toFixed(2)
      });
    }
    
    return mockData;
  }
});